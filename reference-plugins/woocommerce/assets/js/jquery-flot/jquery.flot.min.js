!function(t){t.color={},t.color.make=function(i,e,o,n){var r={};return r.r=i||0,r.g=e||0,r.b=o||0,r.a=null!=n?n:1,r.add=function(t,i){for(var e=0;e<t.length;++e)r[t.charAt(e)]+=i;return r.normalize()},r.scale=function(t,i){for(var e=0;e<t.length;++e)r[t.charAt(e)]*=i;return r.normalize()},r.toString=function(){return r.a>=1?"rgb("+[r.r,r.g,r.b].join(",")+")":"rgba("+[r.r,r.g,r.b,r.a].join(",")+")"},r.normalize=function(){function t(t,i,e){return i<t?t:i>e?e:i}return r.r=t(0,parseInt(r.r),255),r.g=t(0,parseInt(r.g),255),r.b=t(0,parseInt(r.b),255),r.a=t(0,r.a,1),r},r.clone=function(){return t.color.make(r.r,r.b,r.g,r.a)},r.normalize()},t.color.extract=function(i,e){var o;do{if(""!=(o=i.css(e).toLowerCase())&&"transparent"!=o)break;i=i.parent()}while(!t.nodeName(i.get(0),"body"));return"rgba(0, 0, 0, 0)"==o&&(o="transparent"),t.color.parse(o)},t.color.parse=function(e){var o,n=t.color.make;if(o=/rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/.exec(e))return n(parseInt(o[1],10),parseInt(o[2],10),parseInt(o[3],10));if(o=/rgba\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]+(?:\.[0-9]+)?)\s*\)/.exec(e))return n(parseInt(o[1],10),parseInt(o[2],10),parseInt(o[3],10),parseFloat(o[4]));if(o=/rgb\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*\)/.exec(e))return n(2.55*parseFloat(o[1]),2.55*parseFloat(o[2]),2.55*parseFloat(o[3]));if(o=/rgba\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\s*\)/.exec(e))return n(2.55*parseFloat(o[1]),2.55*parseFloat(o[2]),2.55*parseFloat(o[3]),parseFloat(o[4]));if(o=/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/.exec(e))return n(parseInt(o[1],16),parseInt(o[2],16),parseInt(o[3],16));if(o=/#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])/.exec(e))return n(parseInt(o[1]+o[1],16),parseInt(o[2]+o[2],16),parseInt(o[3]+o[3],16));if("string"==typeof e)var r=e.trim().toLowerCase();else r="";return"transparent"==r?n(255,255,255,0):n((o=i[r]||[0,0,0])[0],o[1],o[2])};var i={aqua:[0,255,255],azure:[240,255,255],beige:[245,245,220],black:[0,0,0],blue:[0,0,255],brown:[165,42,42],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgrey:[169,169,169],darkgreen:[0,100,0],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkviolet:[148,0,211],fuchsia:[255,0,255],gold:[255,215,0],green:[0,128,0],indigo:[75,0,130],khaki:[240,230,140],lightblue:[173,216,230],lightcyan:[224,255,255],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightyellow:[255,255,224],lime:[0,255,0],magenta:[255,0,255],maroon:[128,0,0],navy:[0,0,128],olive:[128,128,0],orange:[255,165,0],pink:[255,192,203],purple:[128,0,128],violet:[128,0,128],red:[255,0,0],silver:[192,192,192],white:[255,255,255],yellow:[255,255,0]}}(jQuery),function(t){var i=Object.prototype.hasOwnProperty;function e(i,e){var o=e.children("."+i)[0];if(null==o&&((o=document.createElement("canvas")).className=i,t(o).css({direction:"ltr",position:"absolute",left:0,top:0}).appendTo(e),!o.getContext)){if(!window.G_vmlCanvasManager)throw new Error("Canvas is not available. If you're using IE with a fall-back such as Excanvas, then there's either a mistake in your conditional include, or the page has no DOCTYPE and is rendering in Quirks Mode.");o=window.G_vmlCanvasManager.initElement(o)}this.element=o;var n=this.context=o.getContext("2d"),r=window.devicePixelRatio||1,a=n.webkitBackingStorePixelRatio||n.mozBackingStorePixelRatio||n.msBackingStorePixelRatio||n.oBackingStorePixelRatio||n.backingStorePixelRatio||1;this.pixelRatio=r/a,this.resize(e.width(),e.height()),this.textContainer=null,this.text={},this._textCache={}}function o(i,o,n,r){var a=[],l={colors:["#edc240","#afd8f8","#cb4b4b","#4da74d","#9440ed"],legend:{show:!0,noColumns:1,labelFormatter:null,labelBoxBorderColor:"#ccc",container:null,position:"ne",margin:5,backgroundColor:null,backgroundOpacity:.85,sorted:null},xaxis:{show:null,position:"bottom",mode:null,font:null,color:null,tickColor:null,transform:null,inverseTransform:null,min:null,max:null,autoscaleMargin:null,ticks:null,tickFormatter:null,labelWidth:null,labelHeight:null,reserveSpace:null,tickLength:null,alignTicksWithAxis:null,tickDecimals:null,tickSize:null,minTickSize:null},yaxis:{autoscaleMargin:.02,position:"left"},xaxes:[],yaxes:[],series:{points:{show:!1,radius:3,lineWidth:2,fill:!0,fillColor:"#ffffff",symbol:"circle"},lines:{lineWidth:2,fill:!1,fillColor:null,steps:!1},bars:{show:!1,lineWidth:2,barWidth:1,fill:!0,fillColor:null,align:"left",horizontal:!1,zero:!0},shadowSize:3,highlightColor:null},grid:{show:!0,aboveData:!1,color:"#545454",backgroundColor:null,borderColor:null,tickColor:null,margin:0,labelMargin:5,axisMargin:8,borderWidth:2,minBorderMargin:null,markings:null,markingsColor:"#f4f4f4",markingsLineWidth:2,clickable:!1,hoverable:!1,autoHighlight:!0,mouseActiveRadius:10},interaction:{redrawOverlayInterval:1e3/60},hooks:{}},s=null,c=null,f=null,h=null,u=null,d=[],p=[],m={left:0,right:0,top:0,bottom:0},x=0,g=0,b={processOptions:[],processRawData:[],processDatapoints:[],processOffset:[],drawBackground:[],drawSeries:[],draw:[],bindEvents:[],drawOverlay:[],shutdown:[]},v=this;function k(t,i){i=[v].concat(i);for(var e=0;e<t.length;++e)t[e].apply(this,i)}function y(i){a=function(i){for(var e=[],o=0;o<i.length;++o){var n=t.extend(!0,{},l.series);null!=i[o].data?(n.data=i[o].data,delete i[o].data,t.extend(!0,n,i[o]),i[o].data=n.data):n.data=i[o],e.push(n)}return e}(i),function(){var i,e=a.length,o=-1;for(i=0;i<a.length;++i){var n=a[i].color;null!=n&&(e--,"number"==typeof n&&n>o&&(o=n))}e<=o&&(e=o+1);var r,s=[],c=l.colors,f=c.length,h=0;for(i=0;i<e;i++)r=t.color.parse(c[i%f]||"#666"),i%f==0&&i&&(h=h>=0?h<.5?-h-.2:0:-h),s[i]=r.scale("rgb",1+h);var u,m=0;for(i=0;i<a.length;++i){if(null==(u=a[i]).color?(u.color=s[m].toString(),++m):"number"==typeof u.color&&(u.color=s[u.color].toString()),null==u.lines.show){var x,g=!0;for(x in u)if(u[x]&&u[x].show){g=!1;break}g&&(u.lines.show=!0)}null==u.lines.zero&&(u.lines.zero=!!u.lines.fill),u.xaxis=C(d,w(u,"x")),u.yaxis=C(p,w(u,"y"))}}(),function(){var i,e,o,n,r,l,s,c,f,h,u,d,p=Number.POSITIVE_INFINITY,m=Number.NEGATIVE_INFINITY,x=Number.MAX_VALUE;function g(t,i,e){i<t.datamin&&i!=-x&&(t.datamin=i),e>t.datamax&&e!=x&&(t.datamax=e)}for(t.each(T(),function(t,i){i.datamin=p,i.datamax=m,i.used=!1}),i=0;i<a.length;++i)(r=a[i]).datapoints={points:[]},k(b.processRawData,[r,r.data,r.datapoints]);for(i=0;i<a.length;++i){if(r=a[i],u=r.data,!(d=r.datapoints.format)){if((d=[]).push({x:!0,number:!0,required:!0}),d.push({y:!0,number:!0,required:!0}),r.bars.show||r.lines.show&&r.lines.fill){var v=!!(r.bars.show&&r.bars.zero||r.lines.show&&r.lines.zero);d.push({y:!0,number:!0,required:!1,defaultValue:0,autoscale:v}),r.bars.horizontal&&(delete d[d.length-1].y,d[d.length-1].x=!0)}r.datapoints.format=d}if(null==r.datapoints.pointsize){r.datapoints.pointsize=d.length,s=r.datapoints.pointsize,l=r.datapoints.points;var y=r.lines.show&&r.lines.steps;for(r.xaxis.used=r.yaxis.used=!0,e=o=0;e<u.length;++e,o+=s){var w=null==(h=u[e]);if(!w)for(n=0;n<s;++n)c=h[n],(f=d[n])&&(f.number&&null!=c&&(c=+c,isNaN(c)?c=null:c==Infinity?c=x:c==-Infinity&&(c=-x)),null==c&&(f.required&&(w=!0),null!=f.defaultValue&&(c=f.defaultValue))),l[o+n]=c;if(w)for(n=0;n<s;++n)null!=(c=l[o+n])&&(f=d[n]).autoscale&&(f.x&&g(r.xaxis,c,c),f.y&&g(r.yaxis,c,c)),l[o+n]=null;else if(y&&o>0&&null!=l[o-s]&&l[o-s]!=l[o]&&l[o-s+1]!=l[o+1]){for(n=0;n<s;++n)l[o+s+n]=l[o+n];l[o+1]=l[o-s+1],o+=s}}}}for(i=0;i<a.length;++i)r=a[i],k(b.processDatapoints,[r,r.datapoints]);for(i=0;i<a.length;++i){r=a[i],l=r.datapoints.points,s=r.datapoints.pointsize,d=r.datapoints.format;var M=p,C=p,S=m,W=m;for(e=0;e<l.length;e+=s)if(null!=l[e])for(n=0;n<s;++n)c=l[e+n],(f=d[n])&&!1!==f.autoscale&&c!=x&&c!=-x&&(f.x&&(c<M&&(M=c),c>S&&(S=c)),f.y&&(c<C&&(C=c),c>W&&(W=c)));if(r.bars.show){var z;switch(r.bars.align){case"left":z=0;break;case"right":z=-r.bars.barWidth;break;case"center":z=-r.bars.barWidth/2;break;default:throw new Error("Invalid bar alignment: "+r.bars.align)}r.bars.horizontal?(C+=z,W+=z+r.bars.barWidth):(M+=z,S+=z+r.bars.barWidth)}g(r.xaxis,M,S),g(r.yaxis,C,W)}t.each(T(),function(t,i){i.datamin==p&&(i.datamin=null),i.datamax==m&&(i.datamax=null)})}()}function w(t,i){var e=t[i+"axis"];return"object"==typeof e&&(e=e.n),"number"!=typeof e&&(e=1),e}function T(){return t.grep(d.concat(p),function(t){return t})}function M(t){var i,e,o={};for(i=0;i<d.length;++i)(e=d[i])&&e.used&&(o["x"+e.n]=e.c2p(t.left));for(i=0;i<p.length;++i)(e=p[i])&&e.used&&(o["y"+e.n]=e.c2p(t.top));return o.x1!==undefined&&(o.x=o.x1),o.y1!==undefined&&(o.y=o.y1),o}function C(i,e){return i[e-1]||(i[e-1]={n:e,direction:i==d?"x":"y",options:t.extend(!0,{},i==d?l.xaxis:l.yaxis)}),i[e-1]}function S(i){var e,o=i.labelWidth,n=i.labelHeight,r=i.options.position,a=i.options.tickLength,c=l.grid.axisMargin,f=l.grid.labelMargin,h="x"==i.direction?d:p,u=t.grep(h,function(t){return t&&t.options.position==r&&t.reserveSpace});if(t.inArray(i,u)==u.length-1&&(c=0),null==a){var x=t.grep(h,function(t){return t&&t.reserveSpace});a=(e=0==t.inArray(i,x))?"full":5}isNaN(+a)||(f+=+a),"x"==i.direction?(n+=f,"bottom"==r?(m.bottom+=n+c,i.box={top:s.height-m.bottom,height:n}):(i.box={top:m.top+c,height:n},m.top+=n+c)):(o+=f,"left"==r?(i.box={left:m.left+c,width:o},m.left+=o+c):(m.right+=o+c,i.box={left:s.width-m.right,width:o})),i.position=r,i.tickLength=a,i.box.padding=f,i.innermost=e}function W(){var e,o=T(),n=l.grid.show;for(var r in m){var c=l.grid.margin||0;m[r]="number"==typeof c?c:c[r]||0}for(var r in k(b.processOffset,[m]),m)"object"==typeof l.grid.borderWidth?m[r]+=n?l.grid.borderWidth[r]:0:m[r]+=n?l.grid.borderWidth:0;if(t.each(o,function(t,i){i.show=i.options.show,null==i.show&&(i.show=i.used),i.reserveSpace=i.show||i.options.reserveSpace,function(t){var i=t.options,e=+(null!=i.min?i.min:t.datamin),o=+(null!=i.max?i.max:t.datamax),n=o-e;if(0==n){var r=0==o?1:.01;null==i.min&&(e-=r),null!=i.max&&null==i.min||(o+=r)}else{var a=i.autoscaleMargin;null!=a&&(null==i.min&&(e-=n*a)<0&&null!=t.datamin&&t.datamin>=0&&(e=0),null==i.max&&(o+=n*a)>0&&null!=t.datamax&&t.datamax<=0&&(o=0))}t.min=e,t.max=o}(i)}),n){var f=t.grep(o,function(t){return t.reserveSpace});for(t.each(f,function(t,i){!function(t){var i,e=t.options;i="number"==typeof e.ticks&&e.ticks>0?e.ticks:.3*Math.sqrt("x"==t.direction?s.width:s.height);var o=(t.max-t.min)/i,n=-Math.floor(Math.log(o)/Math.LN10),r=e.tickDecimals;null!=r&&n>r&&(n=r);var a,l=Math.pow(10,-n),c=o/l;c<1.5?a=1:c<3?(a=2,c>2.25&&(null==r||n+1<=r)&&(a=2.5,++n)):a=c<7.5?5:10;a*=l,null!=e.minTickSize&&a<e.minTickSize&&(a=e.minTickSize);if(t.delta=o,t.tickDecimals=Math.max(0,null!=r?r:n),t.tickSize=e.tickSize||a,"time"==e.mode&&!t.tickGenerator)throw new Error("Time mode requires the flot.time plugin.");t.tickGenerator||(t.tickGenerator=function(t){var i,e,o,n=[],r=(e=t.min,(o=t.tickSize)*Math.floor(e/o)),a=0,l=Number.NaN;do{i=l,l=r+a*t.tickSize,n.push(l),++a}while(l<t.max&&l!=i);return n},t.tickFormatter=function(t,i){var e=i.tickDecimals?Math.pow(10,i.tickDecimals):1,o=""+Math.round(t*e)/e;if(null!=i.tickDecimals){var n=o.indexOf("."),r=-1==n?0:o.length-n-1;if(r<i.tickDecimals)return(r?o:o+".")+(""+e).substr(1,i.tickDecimals-r)}return o});"function"==typeof e.tickFormatter&&(t.tickFormatter=function(t,i){return""+e.tickFormatter(t,i)});if(null!=e.alignTicksWithAxis){var f=("x"==t.direction?d:p)[e.alignTicksWithAxis-1];if(f&&f.used&&f!=t){var h=t.tickGenerator(t);if(h.length>0&&(null==e.min&&(t.min=Math.min(t.min,h[0])),null==e.max&&h.length>1&&(t.max=Math.max(t.max,h[h.length-1]))),t.tickGenerator=function(t){var i,e,o=[];for(e=0;e<f.ticks.length;++e)i=(f.ticks[e].v-f.min)/(f.max-f.min),i=t.min+i*(t.max-t.min),o.push(i);return o},!t.mode&&null==e.tickDecimals){var u=Math.max(0,1-Math.floor(Math.log(t.delta)/Math.LN10)),m=t.tickGenerator(t);m.length>1&&/\..*0$/.test((m[1]-m[0]).toFixed(u))||(t.tickDecimals=u)}}}}(i),function(t){var i,e,o=t.options.ticks,n=[];null==o||"number"==typeof o&&o>0?n=t.tickGenerator(t):o&&(n="function"==typeof o?o(t):o);for(t.ticks=[],i=0;i<n.length;++i){var r=null,a=n[i];"object"==typeof a?(e=+a[0],a.length>1&&(r=a[1])):e=+a,null==r&&(r=t.tickFormatter(e,t)),isNaN(e)||t.ticks.push({v:e,label:r})}}(i),function(t,i){t.options.autoscaleMargin&&i.length>0&&(null==t.options.min&&(t.min=Math.min(t.min,i[0].v)),null==t.options.max&&i.length>1&&(t.max=Math.max(t.max,i[i.length-1].v)))}(i,i.ticks),function(t){var i=t.options,e=t.ticks||[],o=i.labelWidth||0,n=i.labelHeight||0,r=o||"x"==t.direction?Math.floor(s.width/(e.length||1)):null;legacyStyles=t.direction+"Axis "+t.direction+t.n+"Axis",layer="flot-"+t.direction+"-axis flot-"+t.direction+t.n+"-axis "+legacyStyles,font=i.font||"flot-tick-label tickLabel";for(var a=0;a<e.length;++a){var l=e[a];if(l.label){var c=s.getTextInfo(layer,l.label,font,null,r);o=Math.max(o,c.width),n=Math.max(n,c.height)}}t.labelWidth=i.labelWidth||o,t.labelHeight=i.labelHeight||n}(i)}),e=f.length-1;e>=0;--e)S(f[e]);!function(){var i,e=l.grid.minBorderMargin,o={x:0,y:0};if(null==e)for(e=0,i=0;i<a.length;++i)e=Math.max(e,2*(a[i].points.radius+a[i].points.lineWidth/2));o.x=o.y=Math.ceil(e),t.each(T(),function(t,i){var e=i.direction;i.reserveSpace&&(o[e]=Math.ceil(Math.max(o[e],("x"==e?i.labelWidth:i.labelHeight)/2)))}),m.left=Math.max(o.x,m.left),m.right=Math.max(o.x,m.right),m.top=Math.max(o.y,m.top),m.bottom=Math.max(o.y,m.bottom)}(),t.each(f,function(t,i){!function(t){"x"==t.direction?(t.box.left=m.left-t.labelWidth/2,t.box.width=s.width-m.left-m.right+t.labelWidth):(t.box.top=m.top-t.labelHeight/2,t.box.height=s.height-m.bottom-m.top+t.labelHeight)}(i)})}x=s.width-m.left-m.right,g=s.height-m.bottom-m.top,t.each(o,function(t,i){!function(t){function i(t){return t}var e,o,n=t.options.transform||i,r=t.options.inverseTransform;"x"==t.direction?(e=t.scale=x/Math.abs(n(t.max)-n(t.min)),o=Math.min(n(t.max),n(t.min))):(e=-(e=t.scale=g/Math.abs(n(t.max)-n(t.min))),o=Math.max(n(t.max),n(t.min))),t.p2c=n==i?function(t){return(t-o)*e}:function(t){return(n(t)-o)*e},t.c2p=r?function(t){return r(o+t/e)}:function(t){return o+t/e}}(i)}),n&&t.each(T(),function(t,i){if(i.show&&0!=i.ticks.length){var e,o,n,r,a,l=i.box,c=i.direction+"Axis "+i.direction+i.n+"Axis",f="flot-"+i.direction+"-axis flot-"+i.direction+i.n+"-axis "+c,h=i.options.font||"flot-tick-label tickLabel";s.removeText(f);for(var u=0;u<i.ticks.length;++u)!(e=i.ticks[u]).label||e.v<i.min||e.v>i.max||("x"==i.direction?(r="center",o=m.left+i.p2c(e.v),"bottom"==i.position?n=l.top+l.padding:(n=l.top+l.height-l.padding,a="bottom")):(a="middle",n=m.top+i.p2c(e.v),"left"==i.position?(o=l.left+l.width-l.padding,r="right"):o=l.left+l.padding),s.addText(f,o,n,e.label,h,null,null,r,a))}}),function(){if(i.find(".legend").remove(),!l.legend.show)return;for(var e,o,n=[],r=[],s=!1,c=l.legend.labelFormatter,f=0;f<a.length;++f)(e=a[f]).label&&(o=c?c(e.label,e):e.label)&&r.push({label:o,color:e.color});if(l.legend.sorted)if("function"==typeof l.legend.sorted)r.sort(l.legend.sorted);else if("reverse"==l.legend.sorted)r.reverse();else{var h="descending"!=l.legend.sorted;r.sort(function(t,i){return t.label==i.label?0:t.label<i.label!=h?1:-1})}for(var f=0;f<r.length;++f){var u=r[f];f%l.legend.noColumns==0&&(s&&n.push("</tr>"),n.push("<tr>"),s=!0),n.push('<td class="legendColorBox"><div style="border:1px solid '+l.legend.labelBoxBorderColor+';padding:1px"><div style="width:4px;height:0;border:5px solid '+u.color+';overflow:hidden"></div></div></td><td class="legendLabel">'+u.label+"</td>")}s&&n.push("</tr>");if(0==n.length)return;var d='<table style="font-size:smaller;color:'+l.grid.color+'">'+n.join("")+"</table>";if(null!=l.legend.container)t(l.legend.container).html(d);else{var p="",x=l.legend.position,g=l.legend.margin;null==g[0]&&(g=[g,g]),"n"==x.charAt(0)?p+="top:"+(g[1]+m.top)+"px;":"s"==x.charAt(0)&&(p+="bottom:"+(g[1]+m.bottom)+"px;"),"e"==x.charAt(1)?p+="right:"+(g[0]+m.right)+"px;":"w"==x.charAt(1)&&(p+="left:"+(g[0]+m.left)+"px;");var b=t('<div class="legend">'+d.replace('style="','style="position:absolute;'+p+";")+"</div>").appendTo(i);if(0!=l.legend.backgroundOpacity){var v=l.legend.backgroundColor;null==v&&((v=(v=l.grid.backgroundColor)&&"string"==typeof v?t.color.parse(v):t.color.extract(b,"background-color")).a=1,v=v.toString());var k=b.children();t('<div style="position:absolute;width:'+k.width()+"px;height:"+k.height()+"px;"+p+"background-color:"+v+';"> </div>').prependTo(b).css("opacity",l.legend.backgroundOpacity)}}}()}function z(){s.clear(),k(b.drawBackground,[h]);var t=l.grid;t.show&&t.backgroundColor&&(h.save(),h.translate(m.left,m.top),h.fillStyle=q(l.grid.backgroundColor,g,0,"rgba(255, 255, 255, 0)"),h.fillRect(0,0,x,g),h.restore()),t.show&&!t.aboveData&&A();for(var i=0;i<a.length;++i)k(b.drawSeries,[h,a[i]]),P(a[i]);k(b.draw,[h]),t.show&&t.aboveData&&A(),s.render(),B()}function I(t,i){for(var e,o,n,r,a=T(),l=0;l<a.length;++l)if((e=a[l]).direction==i&&(t[r=i+e.n+"axis"]||1!=e.n||(r=i+"axis"),t[r])){o=t[r].from,n=t[r].to;break}if(t[r]||(e="x"==i?d[0]:p[0],o=t[i+"1"],n=t[i+"2"]),null!=o&&null!=n&&o>n){var s=o;o=n,n=s}return{from:o,to:n,axis:e}}function A(){var t,i,e,o;h.save(),h.translate(m.left,m.top);var n=l.grid.markings;if(n)for("function"==typeof n&&((i=v.getAxes()).xmin=i.xaxis.min,i.xmax=i.xaxis.max,i.ymin=i.yaxis.min,i.ymax=i.yaxis.max,n=n(i)),t=0;t<n.length;++t){var r=n[t],a=I(r,"x"),s=I(r,"y");null==a.from&&(a.from=a.axis.min),null==a.to&&(a.to=a.axis.max),null==s.from&&(s.from=s.axis.min),null==s.to&&(s.to=s.axis.max),a.to<a.axis.min||a.from>a.axis.max||s.to<s.axis.min||s.from>s.axis.max||(a.from=Math.max(a.from,a.axis.min),a.to=Math.min(a.to,a.axis.max),s.from=Math.max(s.from,s.axis.min),s.to=Math.min(s.to,s.axis.max),a.from==a.to&&s.from==s.to||(a.from=a.axis.p2c(a.from),a.to=a.axis.p2c(a.to),s.from=s.axis.p2c(s.from),s.to=s.axis.p2c(s.to),a.from==a.to||s.from==s.to?(h.beginPath(),h.strokeStyle=r.color||l.grid.markingsColor,h.lineWidth=r.lineWidth||l.grid.markingsLineWidth,h.moveTo(a.from,s.from),h.lineTo(a.to,s.to),h.stroke()):(h.fillStyle=r.color||l.grid.markingsColor,h.fillRect(a.from,s.to,a.to-a.from,s.from-s.to))))}i=T(),e=l.grid.borderWidth;for(var c=0;c<i.length;++c){var f,u,d,p,b=i[c],k=b.box,y=b.tickLength;if(b.show&&0!=b.ticks.length){for(h.lineWidth=1,"x"==b.direction?(f=0,u="full"==y?"top"==b.position?0:g:k.top-m.top+("top"==b.position?k.height:0)):(u=0,f="full"==y?"left"==b.position?0:x:k.left-m.left+("left"==b.position?k.width:0)),b.innermost||(h.strokeStyle=b.options.color,h.beginPath(),d=p=0,"x"==b.direction?d=x+1:p=g+1,1==h.lineWidth&&("x"==b.direction?u=Math.floor(u)+.5:f=Math.floor(f)+.5),h.moveTo(f,u),h.lineTo(f+d,u+p),h.stroke()),h.strokeStyle=b.options.tickColor,h.beginPath(),t=0;t<b.ticks.length;++t){var w=b.ticks[t].v;d=p=0,isNaN(w)||w<b.min||w>b.max||"full"==y&&("object"==typeof e&&e[b.position]>0||e>0)&&(w==b.min||w==b.max)||("x"==b.direction?(f=b.p2c(w),p="full"==y?-g:y,"top"==b.position&&(p=-p)):(u=b.p2c(w),d="full"==y?-x:y,"left"==b.position&&(d=-d)),1==h.lineWidth&&("x"==b.direction?f=Math.floor(f)+.5:u=Math.floor(u)+.5),h.moveTo(f,u),h.lineTo(f+d,u+p))}h.stroke()}}e&&(o=l.grid.borderColor,"object"==typeof e||"object"==typeof o?("object"!=typeof e&&(e={top:e,right:e,bottom:e,left:e}),"object"!=typeof o&&(o={top:o,right:o,bottom:o,left:o}),e.top>0&&(h.strokeStyle=o.top,h.lineWidth=e.top,h.beginPath(),h.moveTo(0-e.left,0-e.top/2),h.lineTo(x,0-e.top/2),h.stroke()),e.right>0&&(h.strokeStyle=o.right,h.lineWidth=e.right,h.beginPath(),h.moveTo(x+e.right/2,0-e.top),h.lineTo(x+e.right/2,g),h.stroke()),e.bottom>0&&(h.strokeStyle=o.bottom,h.lineWidth=e.bottom,h.beginPath(),h.moveTo(x+e.right,g+e.bottom/2),h.lineTo(0,g+e.bottom/2),h.stroke()),e.left>0&&(h.strokeStyle=o.left,h.lineWidth=e.left,h.beginPath(),h.moveTo(0-e.left/2,g+e.bottom),h.lineTo(0-e.left/2,0),h.stroke())):(h.lineWidth=e,h.strokeStyle=l.grid.borderColor,h.strokeRect(-e/2,-e/2,x+e,g+e))),h.restore()}function P(t){t.lines.show&&function(t){function i(t,i,e,o,n){var r=t.points,a=t.pointsize,l=null,s=null;h.beginPath();for(var c=a;c<r.length;c+=a){var f=r[c-a],u=r[c-a+1],d=r[c],p=r[c+1];if(null!=f&&null!=d){if(u<=p&&u<n.min){if(p<n.min)continue;f=(n.min-u)/(p-u)*(d-f)+f,u=n.min}else if(p<=u&&p<n.min){if(u<n.min)continue;d=(n.min-u)/(p-u)*(d-f)+f,p=n.min}if(u>=p&&u>n.max){if(p>n.max)continue;f=(n.max-u)/(p-u)*(d-f)+f,u=n.max}else if(p>=u&&p>n.max){if(u>n.max)continue;d=(n.max-u)/(p-u)*(d-f)+f,p=n.max}if(f<=d&&f<o.min){if(d<o.min)continue;u=(o.min-f)/(d-f)*(p-u)+u,f=o.min}else if(d<=f&&d<o.min){if(f<o.min)continue;p=(o.min-f)/(d-f)*(p-u)+u,d=o.min}if(f>=d&&f>o.max){if(d>o.max)continue;u=(o.max-f)/(d-f)*(p-u)+u,f=o.max}else if(d>=f&&d>o.max){if(f>o.max)continue;p=(o.max-f)/(d-f)*(p-u)+u,d=o.max}f==l&&u==s||h.moveTo(o.p2c(f)+i,n.p2c(u)+e),l=d,s=p,h.lineTo(o.p2c(d)+i,n.p2c(p)+e)}}h.stroke()}h.save(),h.translate(m.left,m.top),h.lineJoin="round";var e=t.lines.lineWidth,o=t.shadowSize;if(e>0&&o>0){h.lineWidth=o,h.strokeStyle="rgba(0,0,0,0.1)";var n=Math.PI/18;i(t.datapoints,Math.sin(n)*(e/2+o/2),Math.cos(n)*(e/2+o/2),t.xaxis,t.yaxis),h.lineWidth=o/2,i(t.datapoints,Math.sin(n)*(e/2+o/4),Math.cos(n)*(e/2+o/4),t.xaxis,t.yaxis)}h.lineWidth=e,h.strokeStyle=t.color;var r=N(t.lines,t.color,0,g);r&&(h.fillStyle=r,function(t,i,e){var o=t.points,n=t.pointsize,r=Math.min(Math.max(0,e.min),e.max),a=0,l=!1,s=1,c=0,f=0;for(;!(n>0&&a>o.length+n);){var u=o[(a+=n)-n],d=o[a-n+s],p=o[a],m=o[a+s];if(l){if(n>0&&null!=u&&null==p){f=a,n=-n,s=2;continue}if(n<0&&a==c+n){h.fill(),l=!1,s=1,a=c=f+(n=-n);continue}}if(null!=u&&null!=p){if(u<=p&&u<i.min){if(p<i.min)continue;d=(i.min-u)/(p-u)*(m-d)+d,u=i.min}else if(p<=u&&p<i.min){if(u<i.min)continue;m=(i.min-u)/(p-u)*(m-d)+d,p=i.min}if(u>=p&&u>i.max){if(p>i.max)continue;d=(i.max-u)/(p-u)*(m-d)+d,u=i.max}else if(p>=u&&p>i.max){if(u>i.max)continue;m=(i.max-u)/(p-u)*(m-d)+d,p=i.max}if(l||(h.beginPath(),h.moveTo(i.p2c(u),e.p2c(r)),l=!0),d>=e.max&&m>=e.max)h.lineTo(i.p2c(u),e.p2c(e.max)),h.lineTo(i.p2c(p),e.p2c(e.max));else if(d<=e.min&&m<=e.min)h.lineTo(i.p2c(u),e.p2c(e.min)),h.lineTo(i.p2c(p),e.p2c(e.min));else{var x=u,g=p;d<=m&&d<e.min&&m>=e.min?(u=(e.min-d)/(m-d)*(p-u)+u,d=e.min):m<=d&&m<e.min&&d>=e.min&&(p=(e.min-d)/(m-d)*(p-u)+u,m=e.min),d>=m&&d>e.max&&m<=e.max?(u=(e.max-d)/(m-d)*(p-u)+u,d=e.max):m>=d&&m>e.max&&d<=e.max&&(p=(e.max-d)/(m-d)*(p-u)+u,m=e.max),u!=x&&h.lineTo(i.p2c(x),e.p2c(d)),h.lineTo(i.p2c(u),e.p2c(d)),h.lineTo(i.p2c(p),e.p2c(m)),p!=g&&(h.lineTo(i.p2c(p),e.p2c(m)),h.lineTo(i.p2c(g),e.p2c(m)))}}}}(t.datapoints,t.xaxis,t.yaxis));e>0&&i(t.datapoints,0,0,t.xaxis,t.yaxis);h.restore()}(t),t.bars.show&&function(t){var i;switch(h.save(),h.translate(m.left,m.top),h.lineWidth=t.bars.lineWidth,h.strokeStyle=t.color,t.bars.align){case"left":i=0;break;case"right":i=-t.bars.barWidth;break;case"center":i=-t.bars.barWidth/2;break;default:throw new Error("Invalid bar alignment: "+t.bars.align)}var e=t.bars.fill?function(i,e){return N(t.bars,t.color,i,e)}:null;(function(i,e,o,n,r,a,l){for(var s=i.points,c=i.pointsize,f=0;f<s.length;f+=c)null!=s[f]&&F(s[f],s[f+1],s[f+2],e,o,n,r,a,l,h,t.bars.horizontal,t.bars.lineWidth)})(t.datapoints,i,i+t.bars.barWidth,0,e,t.xaxis,t.yaxis),h.restore()}(t),t.points.show&&function(t){function i(t,i,e,o,n,r,a,l){for(var s=t.points,c=t.pointsize,f=0;f<s.length;f+=c){var u=s[f],d=s[f+1];null==u||u<r.min||u>r.max||d<a.min||d>a.max||(h.beginPath(),u=r.p2c(u),d=a.p2c(d)+o,"circle"==l?h.arc(u,d,i,0,n?Math.PI:2*Math.PI,!1):l(h,u,d,i,n),h.closePath(),e&&(h.fillStyle=e,h.fill()),h.stroke())}}h.save(),h.translate(m.left,m.top);var e=t.points.lineWidth,o=t.shadowSize,n=t.points.radius,r=t.points.symbol;0==e&&(e=1e-4);if(e>0&&o>0){var a=o/2;h.lineWidth=a,h.strokeStyle="rgba(0,0,0,0.1)",i(t.datapoints,n,null,a+a/2,!0,t.xaxis,t.yaxis,r),h.strokeStyle="rgba(0,0,0,0.2)",i(t.datapoints,n,null,a/2,!0,t.xaxis,t.yaxis,r)}h.lineWidth=e,h.strokeStyle=t.color,i(t.datapoints,n,N(t.points,t.color),0,!1,t.xaxis,t.yaxis,r),h.restore()}(t)}function F(t,i,e,o,n,r,a,l,s,c,f,h){var u,d,p,m,x,g,b,v,k;f?(v=g=b=!0,x=!1,m=i+o,p=i+n,(d=t)<(u=e)&&(k=d,d=u,u=k,x=!0,g=!1)):(x=g=b=!0,v=!1,u=t+o,d=t+n,(m=i)<(p=e)&&(k=m,m=p,p=k,v=!0,b=!1)),d<l.min||u>l.max||m<s.min||p>s.max||(u<l.min&&(u=l.min,x=!1),d>l.max&&(d=l.max,g=!1),p<s.min&&(p=s.min,v=!1),m>s.max&&(m=s.max,b=!1),u=l.p2c(u),p=s.p2c(p),d=l.p2c(d),m=s.p2c(m),a&&(c.beginPath(),c.moveTo(u,p),c.lineTo(u,m),c.lineTo(d,m),c.lineTo(d,p),c.fillStyle=a(p,m),c.fill()),h>0&&(x||g||b||v)&&(c.beginPath(),c.moveTo(u,p+r),x?c.lineTo(u,m+r):c.moveTo(u,m+r),b?c.lineTo(d,m+r):c.moveTo(d,m+r),g?c.lineTo(d,p+r):c.moveTo(d,p+r),v?c.lineTo(u,p+r):c.moveTo(u,p+r),c.stroke()))}function N(i,e,o,n){var r=i.fill;if(!r)return null;if(i.fillColor)return q(i.fillColor,o,n,e);var a=t.color.parse(e);return a.a="number"==typeof r?r:.4,a.normalize(),a.toString()}v.setData=y,v.setupGrid=W,v.draw=z,v.getPlaceholder=function(){return i},v.getCanvas=function(){return s.element},v.getPlotOffset=function(){return m},v.width=function(){return x},v.height=function(){return g},v.offset=function(){var t=f.offset();return t.left+=m.left,t.top+=m.top,t},v.getData=function(){return a},v.getAxes=function(){var i={};return t.each(d.concat(p),function(t,e){e&&(i[e.direction+(1!=e.n?e.n:"")+"axis"]=e)}),i},v.getXAxes=function(){return d},v.getYAxes=function(){return p},v.c2p=M,v.p2c=function(t){var i,e,o,n={};for(i=0;i<d.length;++i)if((e=d[i])&&e.used&&(o="x"+e.n,null==t[o]&&1==e.n&&(o="x"),null!=t[o])){n.left=e.p2c(t[o]);break}for(i=0;i<p.length;++i)if((e=p[i])&&e.used&&(o="y"+e.n,null==t[o]&&1==e.n&&(o="y"),null!=t[o])){n.top=e.p2c(t[o]);break}return n},v.getOptions=function(){return l},v.highlight=G,v.unhighlight=_,v.triggerRedrawOverlay=B,v.pointOffset=function(t){return{left:parseInt(d[w(t,"x")-1].p2c(+t.x)+m.left,10),top:parseInt(p[w(t,"y")-1].p2c(+t.y)+m.top,10)}},v.shutdown=function(){L&&clearTimeout(L);f.off("mousemove",O),f.off("mouseleave",E),f.off("click",R),k(b.shutdown,[f])},v.resize=function(){var t=i.width(),e=i.height();s.resize(t,e),c.resize(t,e)},v.hooks=b,function(){for(var i={Canvas:e},o=0;o<r.length;++o){var n=r[o];n.init(v,i),n.options&&t.extend(!0,l,n.options)}}(),function(e){t.extend(!0,l,e),e&&e.colors&&(l.colors=e.colors);null==l.xaxis.color&&(l.xaxis.color=t.color.parse(l.grid.color).scale("a",.22).toString());null==l.yaxis.color&&(l.yaxis.color=t.color.parse(l.grid.color).scale("a",.22).toString());null==l.xaxis.tickColor&&(l.xaxis.tickColor=l.grid.tickColor||l.xaxis.color);null==l.yaxis.tickColor&&(l.yaxis.tickColor=l.grid.tickColor||l.yaxis.color);null==l.grid.borderColor&&(l.grid.borderColor=l.grid.color);null==l.grid.tickColor&&(l.grid.tickColor=t.color.parse(l.grid.color).scale("a",.22).toString());var o,n,r,a={style:i.css("font-style"),size:Math.round(.8*(+i.css("font-size").replace("px","")||13)),variant:i.css("font-variant"),weight:i.css("font-weight"),family:i.css("font-family")};for(a.lineHeight=1.15*a.size,r=l.xaxes.length||1,o=0;o<r;++o)(n=l.xaxes[o])&&!n.tickColor&&(n.tickColor=n.color),n=t.extend(!0,{},l.xaxis,n),l.xaxes[o]=n,n.font&&(n.font=t.extend({},a,n.font),n.font.color||(n.font.color=n.color));for(r=l.yaxes.length||1,o=0;o<r;++o)(n=l.yaxes[o])&&!n.tickColor&&(n.tickColor=n.color),n=t.extend(!0,{},l.yaxis,n),l.yaxes[o]=n,n.font&&(n.font=t.extend({},a,n.font),n.font.color||(n.font.color=n.color));l.xaxis.noTicks&&null==l.xaxis.ticks&&(l.xaxis.ticks=l.xaxis.noTicks);l.yaxis.noTicks&&null==l.yaxis.ticks&&(l.yaxis.ticks=l.yaxis.noTicks);l.x2axis&&(l.xaxes[1]=t.extend(!0,{},l.xaxis,l.x2axis),l.xaxes[1].position="top");l.y2axis&&(l.yaxes[1]=t.extend(!0,{},l.yaxis,l.y2axis),l.yaxes[1].position="right");l.grid.coloredAreas&&(l.grid.markings=l.grid.coloredAreas);l.grid.coloredAreasColor&&(l.grid.markingsColor=l.grid.coloredAreasColor);l.lines&&t.extend(!0,l.series.lines,l.lines);l.points&&t.extend(!0,l.series.points,l.points);l.bars&&t.extend(!0,l.series.bars,l.bars);null!=l.shadowSize&&(l.series.shadowSize=l.shadowSize);null!=l.highlightColor&&(l.series.highlightColor=l.highlightColor);for(o=0;o<l.xaxes.length;++o)C(d,o+1).options=l.xaxes[o];for(o=0;o<l.yaxes.length;++o)C(p,o+1).options=l.yaxes[o];for(var s in b)l.hooks[s]&&l.hooks[s].length&&(b[s]=b[s].concat(l.hooks[s]));k(b.processOptions,[l])}(n),function(){i.css("padding",0).children(":not(.flot-base,.flot-overlay)").remove(),"static"==i.css("position")&&i.css("position","relative");s=new e("flot-base",i),c=new e("flot-overlay",i),h=s.context,u=c.context,f=t(c.element).off();var o=i.data("plot");o&&(o.shutdown(),c.clear());i.data("plot",v)}(),y(o),W(),z(),function(){l.grid.hoverable&&(f.mousemove(O),f.on("mouseleave",E));l.grid.clickable&&f.on("click",R);k(b.bindEvents,[f])}();var D=[],L=null;function O(t){l.grid.hoverable&&j("plothover",t,function(t){return 0!=t.hoverable})}function E(t){l.grid.hoverable&&j("plothover",t,function(t){return!1})}function R(t){j("plotclick",t,function(t){return 0!=t.clickable})}function j(t,e,o){var n=f.offset(),r=e.pageX-n.left-m.left,s=e.pageY-n.top-m.top,c=M({left:r,top:s});c.pageX=e.pageX,c.pageY=e.pageY;var h=function(t,i,e){var o,n,r,s=l.grid.mouseActiveRadius,c=s*s+1,f=null;for(o=a.length-1;o>=0;--o)if(e(a[o])){var h=a[o],u=h.xaxis,d=h.yaxis,p=h.datapoints.points,m=u.c2p(t),x=d.c2p(i),g=s/u.scale,b=s/d.scale;if(r=h.datapoints.pointsize,u.options.inverseTransform&&(g=Number.MAX_VALUE),d.options.inverseTransform&&(b=Number.MAX_VALUE),h.lines.show||h.points.show)for(n=0;n<p.length;n+=r){var v=p[n],k=p[n+1];if(null!=v&&!(v-m>g||v-m<-g||k-x>b||k-x<-b)){var y=Math.abs(u.p2c(v)-t),w=Math.abs(d.p2c(k)-i),T=y*y+w*w;T<c&&(c=T,f=[o,n/r])}}if(h.bars.show&&!f){var M="left"==h.bars.align?0:-h.bars.barWidth/2,C=M+h.bars.barWidth;for(n=0;n<p.length;n+=r){v=p[n],k=p[n+1];var S=p[n+2];null!=v&&(a[o].bars.horizontal?m<=Math.max(S,v)&&m>=Math.min(S,v)&&x>=k+M&&x<=k+C:m>=v+M&&m<=v+C&&x>=Math.min(S,k)&&x<=Math.max(S,k))&&(f=[o,n/r])}}}return f?(o=f[0],n=f[1],r=a[o].datapoints.pointsize,{datapoint:a[o].datapoints.points.slice(n*r,(n+1)*r),dataIndex:n,series:a[o],seriesIndex:o}):null}(r,s,o);if(h&&(h.pageX=parseInt(h.series.xaxis.p2c(h.datapoint[0])+n.left+m.left,10),h.pageY=parseInt(h.series.yaxis.p2c(h.datapoint[1])+n.top+m.top,10)),l.grid.autoHighlight){for(var u=0;u<D.length;++u){var d=D[u];d.auto!=t||h&&d.series==h.series&&d.point[0]==h.datapoint[0]&&d.point[1]==h.datapoint[1]||_(d.series,d.point)}h&&G(h.series,h.datapoint,t)}i.trigger(t,[c,h])}function B(){var t=l.interaction.redrawOverlayInterval;-1!=t?L||(L=setTimeout(H,t)):H()}function H(){var t,i;for(L=null,u.save(),c.clear(),u.translate(m.left,m.top),t=0;t<D.length;++t)(i=D[t]).series.bars.show?Y(i.series,i.point):X(i.series,i.point);u.restore(),k(b.drawOverlay,[u])}function G(t,i,e){if("number"==typeof t&&(t=a[t]),"number"==typeof i){var o=t.datapoints.pointsize;i=t.datapoints.points.slice(o*i,o*(i+1))}var n=V(t,i);-1==n?(D.push({series:t,point:i,auto:e}),B()):e||(D[n].auto=!1)}function _(t,i){if(null==t&&null==i)return D=[],void B();if("number"==typeof t&&(t=a[t]),"number"==typeof i){var e=t.datapoints.pointsize;i=t.datapoints.points.slice(e*i,e*(i+1))}var o=V(t,i);-1!=o&&(D.splice(o,1),B())}function V(t,i){for(var e=0;e<D.length;++e){var o=D[e];if(o.series==t&&o.point[0]==i[0]&&o.point[1]==i[1])return e}return-1}function X(i,e){var o=e[0],n=e[1],r=i.xaxis,a=i.yaxis,l="string"==typeof i.highlightColor?i.highlightColor:t.color.parse(i.color).scale("a",.5).toString();if(!(o<r.min||o>r.max||n<a.min||n>a.max)){var s=i.points.radius+i.points.lineWidth/2;u.lineWidth=s,u.strokeStyle=l;var c=1.5*s;o=r.p2c(o),n=a.p2c(n),u.beginPath(),"circle"==i.points.symbol?u.arc(o,n,c,0,2*Math.PI,!1):i.points.symbol(u,o,n,c,!1),u.closePath(),u.stroke()}}function Y(i,e){var o="string"==typeof i.highlightColor?i.highlightColor:t.color.parse(i.color).scale("a",.5).toString(),n=o,r="left"==i.bars.align?0:-i.bars.barWidth/2;u.lineWidth=i.bars.lineWidth,u.strokeStyle=o,F(e[0],e[1],e[2]||0,r,r+i.bars.barWidth,0,function(){return n},i.xaxis,i.yaxis,u,i.bars.horizontal,i.bars.lineWidth)}function q(i,e,o,n){if("string"==typeof i)return i;for(var r=h.createLinearGradient(0,o,0,e),a=0,l=i.colors.length;a<l;++a){var s=i.colors[a];if("string"!=typeof s){var c=t.color.parse(n);null!=s.brightness&&(c=c.scale("rgb",s.brightness)),null!=s.opacity&&(c.a*=s.opacity),s=c.toString()}r.addColorStop(a/(l-1),s)}return r}}e.prototype.resize=function(t,i){if(t<=0||i<=0)throw new Error("Invalid dimensions for plot, width = "+t+", height = "+i);var e=this.element,o=this.context,n=this.pixelRatio;this.width!=t&&(e.width=t*n,e.style.width=t+"px",this.width=t),this.height!=i&&(e.height=i*n,e.style.height=i+"px",this.height=i),o.restore(),o.save(),o.scale(n,n)},e.prototype.clear=function(){this.context.clearRect(0,0,this.width,this.height)},e.prototype.render=function(){var t=this._textCache;for(var e in t)if(i.call(t,e)){var o=this.getTextLayer(e),n=t[e];for(var r in o.hide(),n)if(i.call(n,r)){var a=n[r];for(var l in a)if(i.call(a,l)){for(var s,c=a[l].positions,f=0;s=c[f];f++)s.active?s.rendered||(o.append(s.element),s.rendered=!0):(c.splice(f--,1),s.rendered&&s.element.detach());0==c.length&&delete a[l]}}o.show()}},e.prototype.getTextLayer=function(i){var e=this.text[i];return null==e&&(null==this.textContainer&&(this.textContainer=t("<div class='flot-text'></div>").css({position:"absolute",top:0,left:0,bottom:0,right:0,"font-size":"smaller",color:"#545454"}).insertAfter(this.element)),e=this.text[i]=t("<div></div>").addClass(i).css({position:"absolute",top:0,left:0,bottom:0,right:0}).appendTo(this.textContainer)),e},e.prototype.getTextInfo=function(i,e,o,n,r){var a,l,s,c;if(e=""+e,a="object"==typeof o?o.style+" "+o.variant+" "+o.weight+" "+o.size+"px/"+o.lineHeight+"px "+o.family:o,null==(l=this._textCache[i])&&(l=this._textCache[i]={}),null==(s=l[a])&&(s=l[a]={}),null==(c=s[e])){var f=t("<div></div>").html(e).css({position:"absolute","max-width":r,top:-9999}).appendTo(this.getTextLayer(i));"object"==typeof o?f.css({font:a,color:o.color}):"string"==typeof o&&f.addClass(o),c=s[e]={width:f.outerWidth(!0),height:f.outerHeight(!0),element:f,positions:[]},f.detach()}return c},e.prototype.addText=function(t,i,e,o,n,r,a,l,s){var c=this.getTextInfo(t,o,n,r,a),f=c.positions;"center"==l?i-=c.width/2:"right"==l&&(i-=c.width),"middle"==s?e-=c.height/2:"bottom"==s&&(e-=c.height);for(var h,u=0;h=f[u];u++)if(h.x==i&&h.y==e)return void(h.active=!0);h={active:!0,rendered:!1,element:f.length?c.element.clone():c.element,x:i,y:e},f.push(h),h.element.css({top:Math.round(e),left:Math.round(i),"text-align":l})},e.prototype.removeText=function(t,e,o,n,r,a){if(null==n){var l=this._textCache[t];if(null!=l)for(var s in l)if(i.call(l,s)){var c=l[s];for(var f in c)if(i.call(c,f))for(var h=c[f].positions,u=0;d=h[u];u++)d.active=!1}}else{var d;for(h=this.getTextInfo(t,n,r,a).positions,u=0;d=h[u];u++)d.x==e&&d.y==o&&(d.active=!1)}},t.plot=function(i,e,n){return new o(t(i),e,n,t.plot.plugins)},t.plot.version="0.8.1",t.plot.plugins=[],t.fn.plot=function(i,e){return this.each(function(){t.plot(this,i,e)})}}(jQuery);
!function(e){var i=10,s=.95;var t={series:{pie:{show:!1,radius:"auto",innerRadius:0,startAngle:1.5,tilt:1,shadow:{left:5,top:15,alpha:.02},offset:{top:0,left:"auto"},stroke:{color:"#fff",width:1},label:{show:"auto",formatter:function(e,i){return"<div style='font-size:x-small;text-align:center;padding:2px;color:"+i.color+";'>"+e+"<br/>"+Math.round(i.percent)+"%</div>"},radius:1,background:{color:null,opacity:0},threshold:0},combine:{threshold:-1,color:null,label:"Other"},highlight:{opacity:.5}}}};e.plot.plugins.push({init:function(r){var a=null,l=null,n=null,o=null,p=null,h=!1,g=null,c=[];function u(i,s,r){h||(h=!0,a=i.getCanvas(),l=e(a).parent(),t=i.getOptions(),i.setData(function(e){for(var i=0,s=0,r=0,a=t.series.pie.combine.color,l=[],n=0;n<e.length;++n){var o=e[n].data;Array.isArray(o)&&1==o.length&&(o=o[0]),Array.isArray(o)?!isNaN(parseFloat(o[1]))&&isFinite(o[1])?o[1]=+o[1]:o[1]=0:o=!isNaN(parseFloat(o))&&isFinite(o)?[1,+o]:[1,0],e[n].data=[o]}for(var n=0;n<e.length;++n)i+=e[n].data[0][1];for(var n=0;n<e.length;++n){var o=e[n].data[0][1];o/i<=t.series.pie.combine.threshold&&(s+=o,r++,a||(a=e[n].color))}for(var n=0;n<e.length;++n){var o=e[n].data[0][1];(r<2||o/i>t.series.pie.combine.threshold)&&l.push({data:[[1,o]],color:e[n].color,label:e[n].label,angle:o*Math.PI*2/i,percent:o/(i/100)})}return r>1&&l.push({data:[[1,s]],color:a,label:t.series.pie.combine.label,angle:s*Math.PI*2/i,percent:s/(i/100)}),l}(i.getData())))}function d(r,a){if(l){var c=r.getPlaceholder().width(),u=r.getPlaceholder().height(),d=l.children().filter(".legend").children().width()||0;g=a,h=!1,n=Math.min(c,u/t.series.pie.tilt)/2,p=u/2+t.series.pie.offset.top,o=c/2,"auto"==t.series.pie.offset.left?t.legend.position.match("w")?o+=d/2:o-=d/2:o+=t.series.pie.offset.left,o<n?o=n:o>c-n&&(o=c-n);var v=r.getData(),b=0;do{b>0&&(n*=s),b+=1,w(),t.series.pie.tilt<=.8&&k()}while(!A()&&b<i);b>=i&&(w(),l.prepend("<div class='error'>Could not draw pie with labels contained inside canvas</div>")),r.setSeries&&r.insertLegend&&(r.setSeries(v),r.insertLegend())}function w(){g.clearRect(0,0,c,u),l.children().filter(".pieLabel, .pieLabelBackground").remove()}function k(){var e=t.series.pie.shadow.left,i=t.series.pie.shadow.top,s=t.series.pie.shadow.alpha,r=t.series.pie.radius>1?t.series.pie.radius:n*t.series.pie.radius;if(!(r>=c/2-e||r*t.series.pie.tilt>=u/2-i||r<=10)){g.save(),g.translate(e,i),g.globalAlpha=s,g.fillStyle="#000",g.translate(o,p),g.scale(1,t.series.pie.tilt);for(var a=1;a<=10;a++)g.beginPath(),g.arc(0,0,r,0,2*Math.PI,!1),g.fill(),r-=a;g.restore()}}function A(){var i=Math.PI*t.series.pie.startAngle,s=t.series.pie.radius>1?t.series.pie.radius:n*t.series.pie.radius;g.save(),g.translate(o,p),g.scale(1,t.series.pie.tilt),g.save();for(var r=i,a=0;a<v.length;++a)v[a].startAngle=r,h(v[a].angle,v[a].color,!0);if(g.restore(),t.series.pie.stroke.width>0){for(g.save(),g.lineWidth=t.series.pie.stroke.width,r=i,a=0;a<v.length;++a)h(v[a].angle,t.series.pie.stroke.color,!1);g.restore()}return f(g),g.restore(),!t.series.pie.label.show||function(){for(var s=i,r=t.series.pie.label.radius>1?t.series.pie.label.radius:n*t.series.pie.label.radius,a=0;a<v.length;++a){if(v[a].percent>=100*t.series.pie.label.threshold&&!h(v[a],s,a))return!1;s+=v[a].angle}return!0;function h(i,s,a){if(0==i.data[0][1])return!0;var n,h=t.legend.labelFormatter,g=t.series.pie.label.formatter;n=h?h(i.label,i):i.label,g&&(n=g(n,i));var d=(s+i.angle+s)/2,f=o+Math.round(Math.cos(d)*r),v=p+Math.round(Math.sin(d)*r)*t.series.pie.tilt,b="<span class='pieLabel' id='pieLabel"+a+"' style='position:absolute;top:"+v+"px;left:"+f+"px;'>"+n+"</span>";l.append(b);var w=l.children("#pieLabel"+a),k=v-w.height()/2,A=f-w.width()/2;if(w.css("top",k),w.css("left",A),0-k>0||0-A>0||u-(k+w.height())<0||c-(A+w.width())<0)return!1;if(0!=t.series.pie.label.background.opacity){var M=t.series.pie.label.background.color;null==M&&(M=i.color);var P="top:"+k+"px;left:"+A+"px;";e("<div class='pieLabelBackground' style='position:absolute;width:"+w.width()+"px;height:"+w.height()+"px;"+P+"background-color:"+M+";'></div>").css("opacity",t.series.pie.label.background.opacity).insertBefore(w)}return!0}}();function h(e,i,t){e<=0||isNaN(e)||(t?g.fillStyle=i:(g.strokeStyle=i,g.lineJoin="round"),g.beginPath(),Math.abs(e-2*Math.PI)>1e-9&&g.moveTo(0,0),g.arc(0,0,s,r,r+e/2,!1),g.arc(0,0,s,r+e/2,r+e,!1),g.closePath(),r+=e,t?g.fill():g.stroke())}}}function f(e){if(t.series.pie.innerRadius>0){e.save();var i=t.series.pie.innerRadius>1?t.series.pie.innerRadius:n*t.series.pie.innerRadius;e.globalCompositeOperation="destination-out",e.beginPath(),e.fillStyle=t.series.pie.stroke.color,e.arc(0,0,i,0,2*Math.PI,!1),e.fill(),e.closePath(),e.restore(),e.save(),e.beginPath(),e.strokeStyle=t.series.pie.stroke.color,e.arc(0,0,i,0,2*Math.PI,!1),e.stroke(),e.closePath(),e.restore()}}function v(e,i){for(var s=!1,t=-1,r=e.length,a=r-1;++t<r;a=t)(e[t][1]<=i[1]&&i[1]<e[a][1]||e[a][1]<=i[1]&&i[1]<e[t][1])&&i[0]<(e[a][0]-e[t][0])*(i[1]-e[t][1])/(e[a][1]-e[t][1])+e[t][0]&&(s=!s);return s}function b(e){k("plothover",e)}function w(e){k("plotclick",e)}function k(e,i){var s=r.offset(),a=function(e,i){for(var s,t,a=r.getData(),l=r.getOptions(),h=l.series.pie.radius>1?l.series.pie.radius:n*l.series.pie.radius,c=0;c<a.length;++c){var u=a[c];if(u.pie.show){if(g.save(),g.beginPath(),g.moveTo(0,0),g.arc(0,0,h,u.startAngle,u.startAngle+u.angle/2,!1),g.arc(0,0,h,u.startAngle+u.angle/2,u.startAngle+u.angle,!1),g.closePath(),s=e-o,t=i-p,g.isPointInPath){if(g.isPointInPath(e-o,i-p))return g.restore(),{datapoint:[u.percent,u.data],dataIndex:0,series:u,seriesIndex:c}}else if(v([[0,0],[h*Math.cos(u.startAngle),h*Math.sin(u.startAngle)],[h*Math.cos(u.startAngle+u.angle/4),h*Math.sin(u.startAngle+u.angle/4)],[h*Math.cos(u.startAngle+u.angle/2),h*Math.sin(u.startAngle+u.angle/2)],[h*Math.cos(u.startAngle+u.angle/1.5),h*Math.sin(u.startAngle+u.angle/1.5)],[h*Math.cos(u.startAngle+u.angle),h*Math.sin(u.startAngle+u.angle)]],[s,t]))return g.restore(),{datapoint:[u.percent,u.data],dataIndex:0,series:u,seriesIndex:c};g.restore()}}return null}(parseInt(i.pageX-s.left),parseInt(i.pageY-s.top));if(t.grid.autoHighlight)for(var h=0;h<c.length;++h){var u=c[h];u.auto!=e||a&&u.series==a.series||A(u.series)}a&&function(e,i){var s=M(e);-1==s?(c.push({series:e,auto:i}),r.triggerRedrawOverlay()):i||(c[s].auto=!1)}(a.series,e);var d={pageX:i.pageX,pageY:i.pageY};l.trigger(e,[d,a])}function A(e){null==e&&(c=[],r.triggerRedrawOverlay());var i=M(e);-1!=i&&(c.splice(i,1),r.triggerRedrawOverlay())}function M(e){for(var i=0;i<c.length;++i)if(c[i].series==e)return i;return-1}r.hooks.processOptions.push(function(e,i){i.series.pie.show&&(i.grid.show=!1,"auto"==i.series.pie.label.show&&(i.legend.show?i.series.pie.label.show=!1:i.series.pie.label.show=!0),"auto"==i.series.pie.radius&&(i.series.pie.label.show?i.series.pie.radius=.75:i.series.pie.radius=1),i.series.pie.tilt>1?i.series.pie.tilt=1:i.series.pie.tilt<0&&(i.series.pie.tilt=0))}),r.hooks.bindEvents.push(function(e,i){var s=e.getOptions();s.series.pie.show&&(s.grid.hoverable&&i.off("mousemove").on("mousemove",b),s.grid.clickable&&i.off("click").on("click",w))}),r.hooks.processDatapoints.push(function(e,i,s,t){e.getOptions().series.pie.show&&u(e)}),r.hooks.drawOverlay.push(function(e,i){e.getOptions().series.pie.show&&function(e,i){var s=e.getOptions(),t=s.series.pie.radius>1?s.series.pie.radius:n*s.series.pie.radius;i.save(),i.translate(o,p),i.scale(1,s.series.pie.tilt);for(var r=0;r<c.length;++r)a(c[r].series);function a(e){e.angle<=0||isNaN(e.angle)||(i.fillStyle="rgba(255, 255, 255, "+s.series.pie.highlight.opacity+")",i.beginPath(),Math.abs(e.angle-2*Math.PI)>1e-9&&i.moveTo(0,0),i.arc(0,0,t,e.startAngle,e.startAngle+e.angle/2,!1),i.arc(0,0,t,e.startAngle+e.angle/2,e.startAngle+e.angle,!1),i.closePath(),i.fill())}f(i),i.restore()}(e,i)}),r.hooks.draw.push(function(e,i){e.getOptions().series.pie.show&&d(e,i)})},options:t,name:"pie",version:"1.1"})}(jQuery);
jQuery(function(t){(function(){var e,n,r,a,i,o,l,u,s,c,h,p,f,g,v,d,m,y,C,T,w,$,S,D=[].slice,k=[].indexOf||function(t){for(var e=0,n=this.length;e<n;e++)if(e in this&&this[e]===t)return e;return-1};(t=window.jQuery||window.Zepto||window.$).payment={},t.payment.fn={},t.fn.payment=function(){var e,n;return n=arguments[0],e=2<=arguments.length?D.call(arguments,1):[],t.payment.fn[n].apply(this,e)},a=/(\d{1,4})/g,t.payment.cards=r=[{type:"maestro",patterns:[5018,502,503,506,56,58,639,6220,67],format:a,length:[12,13,14,15,16,17,18,19],cvcLength:[3],luhn:!0},{type:"forbrugsforeningen",patterns:[600],format:a,length:[16],cvcLength:[3],luhn:!0},{type:"dankort",patterns:[5019],format:a,length:[16],cvcLength:[3],luhn:!0},{type:"visa",patterns:[4],format:a,length:[13,16],cvcLength:[3],luhn:!0},{type:"mastercard",patterns:[51,52,53,54,55,22,23,24,25,26,27],format:a,length:[16],cvcLength:[3],luhn:!0},{type:"amex",patterns:[34,37],format:/(\d{1,4})(\d{1,6})?(\d{1,5})?/,length:[15],cvcLength:[3,4],luhn:!0},{type:"dinersclub",patterns:[30,36,38,39],format:/(\d{1,4})(\d{1,6})?(\d{1,4})?/,length:[14],cvcLength:[3],luhn:!0},{type:"discover",patterns:[60,64,65,622],format:a,length:[16],cvcLength:[3],luhn:!0},{type:"unionpay",patterns:[62,88],format:a,length:[16,17,18,19],cvcLength:[3],luhn:!1},{type:"jcb",patterns:[35],format:a,length:[16],cvcLength:[3],luhn:!0}],e=function(t){var e,n,a,i,o,l,u;for(t=(t+"").replace(/\D/g,""),a=0,o=r.length;a<o;a++)for(i=0,l=(u=(e=r[a]).patterns).length;i<l;i++)if(n=u[i]+"",t.substr(0,n.length)===n)return e},n=function(t){var e,n,a;for(n=0,a=r.length;n<a;n++)if((e=r[n]).type===t)return e},p=function(t){var e,n,r,a,i,o;for(r=!0,a=0,i=0,o=(n=(t+"").split("").reverse()).length;i<o;i++)e=n[i],e=parseInt(e,10),(r=!r)&&(e*=2),e>9&&(e-=9),a+=e;return a%10==0},h=function(t){var e;return null!=t.prop("selectionStart")&&t.prop("selectionStart")!==t.prop("selectionEnd")||!(null==("undefined"!=typeof document&&null!==document&&null!=(e=document.selection)?e.createRange:void 0)||!document.selection.createRange().text)},$=function(t,e){var n,r,a,i,o;try{r=e.prop("selectionStart")}catch(l){l,r=null}if(i=e.val(),e.val(t),null!==r&&e.is(":focus"))return r===i.length&&(r=t.length),i!==t&&(o=i.slice(r-1,+r+1||9e9),n=t.slice(r-1,+r+1||9e9),a=t[r],/\d/.test(a)&&o===a+" "&&n===" "+a&&(r+=1)),e.prop("selectionStart",r),e.prop("selectionEnd",r)},m=function(t){var e,n,r,a,i,o;for(null==t&&(t=""),"０１２３４５６７８９","0123456789",a="",i=0,o=(e=t.split("")).length;i<o;i++)n=e[i],(r="０１２３４５６７８９".indexOf(n))>-1&&(n="0123456789"[r]),a+=n;return a},d=function(e){var n;return n=t(e.currentTarget),setTimeout(function(){var t;return t=n.val(),t=(t=m(t)).replace(/\D/g,""),$(t,n)})},g=function(e){var n;return n=t(e.currentTarget),setTimeout(function(){var e;return e=n.val(),e=m(e),e=t.payment.formatCardNumber(e),$(e,n)})},l=function(n){var r,a,i,o,l,u,s;if(i=String.fromCharCode(n.which),/^\d+$/.test(i)&&(r=t(n.currentTarget),s=r.val(),a=e(s+i),o=(s.replace(/\D/g,"")+i).length,u=16,a&&(u=a.length[a.length.length-1]),!(o>=u||null!=r.prop("selectionStart")&&r.prop("selectionStart")!==s.length)))return(l=a&&"amex"===a.type?/^(\d{4}|\d{4}\s\d{6})$/:/(?:^|\s)(\d{4})$/).test(s)?(n.preventDefault(),setTimeout(function(){return r.val(s+" "+i)})):l.test(s+i)?(n.preventDefault(),setTimeout(function(){return r.val(s+i+" ")})):void 0},i=function(e){var n,r;if(n=t(e.currentTarget),r=n.val(),8===e.which&&(null==n.prop("selectionStart")||n.prop("selectionStart")===r.length))return/\d\s$/.test(r)?(e.preventDefault(),setTimeout(function(){return n.val(r.replace(/\d\s$/,""))})):/\s\d?$/.test(r)?(e.preventDefault(),setTimeout(function(){return n.val(r.replace(/\d$/,""))})):void 0},v=function(e){var n;return n=t(e.currentTarget),setTimeout(function(){var e;return e=n.val(),e=m(e),e=t.payment.formatExpiry(e),$(e,n)})},u=function(e){var n,r,a;if(r=String.fromCharCode(e.which),/^\d+$/.test(r))return n=t(e.currentTarget),a=n.val()+r,/^\d$/.test(a)&&"0"!==a&&"1"!==a?(e.preventDefault(),setTimeout(function(){return n.val("0"+a+" / ")})):/^\d\d$/.test(a)?(e.preventDefault(),setTimeout(function(){var t,e;return t=parseInt(a[0],10),(e=parseInt(a[1],10))>2&&0!==t?n.val("0"+t+" / "+e):n.val(a+" / ")})):void 0},s=function(e){var n,r,a;if(r=String.fromCharCode(e.which),/^\d+$/.test(r))return a=(n=t(e.currentTarget)).val(),/^\d\d$/.test(a)?n.val(a+" / "):void 0},c=function(e){var n,r,a;if("/"===(a=String.fromCharCode(e.which))||" "===a)return r=(n=t(e.currentTarget)).val(),/^\d$/.test(r)&&"0"!==r?n.val("0"+r+" / "):void 0},o=function(e){var n,r;if(n=t(e.currentTarget),r=n.val(),8===e.which&&(null==n.prop("selectionStart")||n.prop("selectionStart")===r.length))return/\d\s\/\s$/.test(r)?(e.preventDefault(),setTimeout(function(){return n.val(r.replace(/\d\s\/\s$/,""))})):void 0},f=function(e){var n;return n=t(e.currentTarget),setTimeout(function(){var t;return t=n.val(),t=(t=m(t)).replace(/\D/g,"").slice(0,4),$(t,n)})},w=function(t){var e;return!(!t.metaKey&&!t.ctrlKey)||32!==t.which&&(0===t.which||(t.which<33||(e=String.fromCharCode(t.which),!!/[\d\s]/.test(e))))},C=function(n){var r,a,i,o;if(r=t(n.currentTarget),i=String.fromCharCode(n.which),/^\d+$/.test(i)&&!h(r))return o=(r.val()+i).replace(/\D/g,""),(a=e(o))?o.length<=a.length[a.length.length-1]:o.length<=16},T=function(e){var n,r;if(n=t(e.currentTarget),r=String.fromCharCode(e.which),/^\d+$/.test(r)&&!h(n))return!((n.val()+r).replace(/\D/g,"").length>6)&&void 0},y=function(e){var n,r;if(n=t(e.currentTarget),r=String.fromCharCode(e.which),/^\d+$/.test(r)&&!h(n))return(n.val()+r).length<=4},S=function(e){var n,a,i,o,l;if(l=(n=t(e.currentTarget)).val(),o=t.payment.cardType(l)||"unknown",!n.hasClass(o))return a=function(){var t,e,n;for(n=[],t=0,e=r.length;t<e;t++)i=r[t],n.push(i.type);return n}(),n.removeClass("unknown"),n.removeClass(a.join(" ")),n.addClass(o),n.toggleClass("identified","unknown"!==o),n.trigger("payment.cardType",o)},t.payment.fn.formatCardCVC=function(){return this.on("keypress",w),this.on("keypress",y),this.on("paste",f),this.on("change",f),this.on("input",f),this},t.payment.fn.formatCardExpiry=function(){return this.on("keypress",w),this.on("keypress",T),this.on("keypress",u),this.on("keypress",c),this.on("keypress",s),this.on("keydown",o),this.on("change",v),this.on("input",v),this},t.payment.fn.formatCardNumber=function(){return this.on("keypress",w),this.on("keypress",C),this.on("keypress",l),this.on("keydown",i),this.on("keyup",S),this.on("paste",g),this.on("change",g),this.on("input",g),this.on("input",S),this},t.payment.fn.restrictNumeric=function(){return this.on("keypress",w),this.on("paste",d),this.on("change",d),this.on("input",d),this},t.payment.fn.cardExpiryVal=function(){return t.payment.cardExpiryVal(t(this).val())},t.payment.cardExpiryVal=function(t){var e,n,r;return e=(r=t.split(/[\s\/]+/,2))[0],2===(null!=(n=r[1])?n.length:void 0)&&/^\d+$/.test(n)&&(n=(new Date).getFullYear().toString().slice(0,2)+n),{month:e=parseInt(e,10),year:n=parseInt(n,10)}},t.payment.validateCardNumber=function(t){var n,r;return t=(t+"").replace(/\s+|-/g,""),!!/^\d+$/.test(t)&&(!!(n=e(t))&&(r=t.length,k.call(n.length,r)>=0&&(!1===n.luhn||p(t))))},t.payment.validateCardExpiry=function(t,e){var n,r,a;return"object"==typeof t&&"month"in t&&(t=(a=t).month,e=a.year),!(!t||!e)&&(t=t||0==t?t.toString().trim():"",e=e||0==e?e.toString().trim():"",!!/^\d+$/.test(t)&&(!!/^\d+$/.test(e)&&(1<=t&&t<=12&&(2===e.length&&(e=e<70?"20"+e:"19"+e),4===e.length&&(r=new Date(e,t),n=new Date,r.setMonth(r.getMonth()-1),r.setMonth(r.getMonth()+1,1),r>n)))))},t.payment.validateCardCVC=function(t,e){var r,a;return t=t||0==t?t.toString().trim():"",!!/^\d+$/.test(t)&&(null!=(r=n(e))?(a=t.length,k.call(r.cvcLength,a)>=0):t.length>=3&&t.length<=4)},t.payment.cardType=function(t){var n;return t&&(null!=(n=e(t))?n.type:void 0)||null},t.payment.formatCardNumber=function(n){var r,a,i,o;return n=n.replace(/\D/g,""),(r=e(n))?(i=r.length[r.length.length-1],n=n.slice(0,i),r.format.global?null!=(o=n.match(r.format))?o.join(" "):void 0:null!=(a=r.format.exec(n))?(a.shift(),(a=t.grep(a,function(t){return t})).join(" ")):void 0):n},t.payment.formatExpiry=function(t){var e,n,r,a;return(n=t.match(/^\D*(\d{1,2})(\D+)?(\d{1,4})?/))?(e=n[1]||"",r=n[2]||"",(a=n[3]||"").length>0?r=" / ":" /"===r?(e=e.substring(0,1),r=""):2===e.length||r.length>0?r=" / ":1===e.length&&"0"!==e&&"1"!==e&&(e="0"+e,r=" / "),e+r+a):""}}).call(this)});
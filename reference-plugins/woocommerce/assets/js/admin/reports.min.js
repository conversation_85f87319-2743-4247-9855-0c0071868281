jQuery(function(t){function e(e,a,n){t('<div class="chart-tooltip">'+n+"</div>").css({top:a-16,left:e+20}).appendTo("body").fadeIn(200)}var a=null,n=null;t(".chart-placeholder").on("plothover",function(r,o,i){if(i){if((a!==i.dataIndex||n!==i.seriesIndex)&&(a=i.dataIndex,n=i.seriesIndex,t(".chart-tooltip").remove(),i.series.points.show||i.series.enable_tooltip)){var s=i.series.data[i.dataIndex][1],h="";i.series.prepend_label&&(h=h+i.series.label+": "),i.series.prepend_tooltip&&(h+=i.series.prepend_tooltip),h+=s,i.series.append_tooltip&&(h+=i.series.append_tooltip),i.series.pie.show?e(o.pageX,o.pageY,h):e(i.pageX,i.pageY,h)}}else t(".chart-tooltip").remove(),a=null});var r=t(".range_datepicker").datepicker({changeMonth:!0,changeYear:!0,defaultDate:"",dateFormat:"yy-mm-dd",numberOfMonths:1,minDate:"-20Y",maxDate:"+1D",showButtonPanel:!0,showOn:"focus",buttonImageOnly:!0,onSelect:function(){var e=t(this).is(".from")?"minDate":"maxDate",a=t(this).datepicker("getDate");r.not(this).datepicker("option",e,a)}});"undefined"==typeof document.createElement("a").download&&t(".export_csv").hide(),t(".export_csv").on("click",function(){var e=t(this).data("exclude_series")||"";e=(e=e.toString()).split(",");var a,n,r,o=t(this).data("xaxes"),i=t(this).data("groupby"),s=t(this).data("index_type"),h="";if("table"===t(this).data("export"))t(this).offsetParent().find("thead tr,tbody tr").each(function(){t(this).find("th, td").each(function(){var e=t(this).text();e=e.replace("[?]","").replace("#",""),h+='"'+e+'",'}),h=h.substring(0,h.length-1),h+="\n"}),t(this).offsetParent().find("tfoot tr").each(function(){t(this).find("th, td").each(function(){var e=t(this).text();if(e=e.replace("[?]","").replace("#",""),h+='"'+e+'",',t(this).attr("colspan")>0)for(c=1;c<t(this).attr("colspan");c++)h+='"",'}),h=h.substring(0,h.length-1),h+="\n"});else{if(!window.main_chart)return!1;var l=window.main_chart.getData(),d=[];for(h+='"'+o+'",',t.each(l,function(a,n){e&&-1!==t.inArray(a.toString(),e)||d.push(n)}),a=0;a<d.length;++a)h+='"'+d[a].label+'",';h=h.substring(0,h.length-1),h+="\n";var p={};for(a=0;a<d.length;++a)for(n=d[a].data,r=0;r<n.length;++r){p[n[r][0]]=[];for(var c=0;c<d.length;++c)p[n[r][0]].push(0)}for(a=0;a<d.length;++a)for(n=d[a].data,r=0;r<n.length;++r)p[n[r][0]][a]=n[r][1];t.each(p,function(t,e){var a=new Date(parseInt(t,10));h+="none"===s?'"'+t+'",':"day"===i?'"'+a.getUTCFullYear()+"-"+parseInt(a.getUTCMonth()+1,10)+"-"+a.getUTCDate()+'",':'"'+a.getUTCFullYear()+"-"+parseInt(a.getUTCMonth()+1,10)+'",';for(var n=0;n<e.length;++n){var r=e[n];Math.round(r)!==r&&(r=(r=parseFloat(r)).toFixed(2)),h+='"'+r+'",'}h=h.substring(0,h.length-1),h+="\n"})}return h="data:text/csv;charset=utf-8,\ufeff"+encodeURIComponent(h),t(this).attr("href",h),!0})});
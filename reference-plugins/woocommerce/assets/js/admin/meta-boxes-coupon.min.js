jQuery(function(e){const o=function(){const o=e("#wc-password-protected-coupon-warning");if(0===o.length)return;const t=e('input[name="visibility"]'),n=e("#visibility-radio-password"),c=e('label[for="visibility-radio-password"]');if(!n.is(":checked"))return n.prop("disabled",!0),void c.css("text-decoration","line-through");t.on("change",function(){o.toggleClass("hidden",!n.is(":checked"))})};({init:function(){e("select#discount_type").on("change",this.type_options).trigger("change"),this.insert_generate_coupon_code_button(),e(".button.generate-coupon-code").on("click",this.generate_coupon_code)},type_options:function(){var o=e(this).val();"percent"===o?e("#coupon_amount").removeClass("wc_input_price").addClass("wc_input_decimal"):e("#coupon_amount").removeClass("wc_input_decimal").addClass("wc_input_price"),"fixed_cart"!==o?e(".limit_usage_to_x_items_field").show():e(".limit_usage_to_x_items_field").hide()},insert_generate_coupon_code_button:function(){const o=e(".post-type-shop_coupon").find("#title"),t=document.createElement("a");t.href="#",t.className="button generate-coupon-code",t.textContent=woocommerce_admin_meta_boxes_coupon.generate_button_text,o.after(t)},generate_coupon_code:function(o){o.preventDefault();for(var t=e("#title"),n=e("#title-prompt-text"),c="",i=0;i<woocommerce_admin_meta_boxes_coupon.char_length;i++)c+=woocommerce_admin_meta_boxes_coupon.characters.charAt(Math.floor(Math.random()*woocommerce_admin_meta_boxes_coupon.characters.length));c=woocommerce_admin_meta_boxes_coupon.prefix+c+woocommerce_admin_meta_boxes_coupon.suffix,t.trigger("focus").val(c),n.addClass("screen-reader-text")}}).init(),o()});
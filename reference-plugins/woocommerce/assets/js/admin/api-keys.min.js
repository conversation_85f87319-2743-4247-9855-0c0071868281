!function(e){new(Backbone.View.extend({el:e("#key-fields"),events:{"click input#update_api_key":"saveKey"},initialize:function(){_.bindAll(this,"saveKey")},block:function(){e(this.el).block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},unblock:function(){e(this.el).unblock()},initTipTip:function(i){e(document.body).on("click",i,function(t){t.preventDefault(),document.queryCommandSupported("copy")?(e("#copy-error").text(""),wcClearClipboard(),wcSetClipboard(e(this).prev("input").val().trim(),e(i))):(e(i).parent().find("input").trigger("focus").trigger("select"),e("#copy-error").text(woocommerce_admin_api_keys.clipboard_failed))}).on("aftercopy",i,function(){e("#copy-error").text(""),e(i).tipTip({attribute:"data-tip",activation:"focus",fadeIn:50,fadeOut:50,delay:0}).trigger("focus")}).on("aftercopyerror",i,function(){e(i).parent().find("input").trigger("focus").trigger("select"),e("#copy-error").text(woocommerce_admin_api_keys.clipboard_failed)})},createQRCode:function(i,t){e("#keys-qrcode").qrcode({text:i+"|"+t,width:120,height:120})},saveKey:function(i){i.preventDefault();var t=this;t.block(),Backbone.ajax({method:"POST",dataType:"json",url:woocommerce_admin_api_keys.ajax_url,data:{action:"woocommerce_update_api_key",security:woocommerce_admin_api_keys.update_api_nonce,key_id:e("#key_id",t.el).val(),description:e("#key_description",t.el).val(),user:e("#key_user",t.el).val(),permissions:e("#key_permissions",t.el).val()},success:function(i){if(e(".wc-api-message",t.el).remove(),i.success){var o=i.data;if(e("h2, h3",t.el).first().append('<div class="wc-api-message updated"><p>'+o.message+"</p></div>"),0<o.consumer_key.length&&0<o.consumer_secret.length){e("#api-keys-options",t.el).remove(),e("p.submit",t.el).empty().append(o.revoke_url);var r=wp.template("api-keys-template");e("p.submit",t.el).before(r({consumer_key:o.consumer_key,consumer_secret:o.consumer_secret})),t.createQRCode(o.consumer_key,o.consumer_secret),t.initTipTip(".copy-key"),t.initTipTip(".copy-secret")}else e("#key_description",t.el).val(o.description),e("#key_user",t.el).val(o.user_id),e("#key_permissions",t.el).val(o.permissions)}else e("h2, h3",t.el).first().append('<div class="wc-api-message error"><p>'+i.data.message+"</p></div>");t.unblock()}})}}))}(jQuery);
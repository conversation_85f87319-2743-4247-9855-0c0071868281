jQuery(function(n){try{n(document.body).on("wc-enhanced-select-init",function(){n(":input.wc-brands-search").filter(":not(.enhanced)").each(function(){var e=n.extend({allowClear:!!n(this).data("allow_clear"),placeholder:n(this).data("placeholder"),minimumInputLength:n(this).data("minimum_input_length")?n(this).data("minimum_input_length"):3,escapeMarkup:function(n){return n},ajax:{url:wpApiSettings.root+"wc/v3/products/brands",dataType:"json",delay:250,headers:{"X-WP-Nonce":wpApiSettings.nonce},data:function(n){return{hide_empty:1,search:n.term}},processResults:function(n){return{results:n.map(n=>({id:n.slug,text:n.name+" ("+n.count+")"}))}},cache:!0}},{language:{errorLoading:function(){return wc_enhanced_select_params.i18n_searching},inputTooLong:function(n){var e=n.input.length-n.maximum;return 1===e?wc_enhanced_select_params.i18n_input_too_long_1:wc_enhanced_select_params.i18n_input_too_long_n.replace("%qty%",e)},inputTooShort:function(n){var e=n.minimum-n.input.length;return 1===e?wc_enhanced_select_params.i18n_input_too_short_1:wc_enhanced_select_params.i18n_input_too_short_n.replace("%qty%",e)},loadingMore:function(){return wc_enhanced_select_params.i18n_load_more},maximumSelected:function(n){return 1===n.maximum?wc_enhanced_select_params.i18n_selection_too_long_1:wc_enhanced_select_params.i18n_selection_too_long_n.replace("%qty%",n.maximum)},noResults:function(){return wc_enhanced_select_params.i18n_no_matches},searching:function(){return wc_enhanced_select_params.i18n_searching}}});n(this).selectWoo(e).addClass("enhanced")})}).trigger("wc-enhanced-select-init")}catch(e){window.console.log(e)}});
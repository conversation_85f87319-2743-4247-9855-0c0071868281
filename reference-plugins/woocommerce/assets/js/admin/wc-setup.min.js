jQuery(function(e){function i(){e(".wc-setup-content").block({message:null,overlayCSS:{background:"#fff",opacity:.6}})}function t(){e("form.activate-jetpack").trigger("submit")}function s(){wp.ajax.post("setup_wizard_check_jetpack").then(function(e){if(!e||!e.is_active||"yes"===e.is_active)return t();setTimeout(s,3e3)}).fail(function(){t()})}function c(i,t,s){var c=t.data("plugins");for(var r in Array.isArray(c)?c:[]){var n=c[r].slug;i[n]=i[n]||e('<span class="plugin-install-info-list-item">').append('<a href="https://wordpress.org/plugins/'+n+'/" target="_blank">'+c[r].name+"</a>"),i[n].find("a").on("mouseenter mouseleave",function(e,i){e.toggleClass("plugin-install-source","mouseenter"===i.type)}.bind(null,s?t.closest(s):t))}}function r(){var i={},t=[];e(".wc-wizard-service-enable input:checked").each(function(){c(i,e(this),".wc-wizard-service-item");var s=e(this).closest(".wc-wizard-service-item");s.find("input.payment-checkbox-input:checked").each(function(){t.push(e(this).attr("id")),c(i,e(this),".wc-wizard-service-settings")}),s.find(".wc-wizard-shipping-method-select .method").each(function(){var t=e(this);"live_rates"===t.val()&&c(i,t,".wc-wizard-service-item")})}),e(".recommended-item input:checked").each(function(){c(i,e(this),".recommended-item")});var s=e("span.plugin-install-info-list").empty();for(var r in i)s.append(i[r]);t&&wc_setup_params.current_step&&wc_setup_params.i18n.extra_plugins[wc_setup_params.current_step]&&wc_setup_params.i18n.extra_plugins[wc_setup_params.current_step][t.join(",")]&&s.append(wc_setup_params.i18n.extra_plugins[wc_setup_params.current_step][t.join(",")]),e("span.plugin-install-info").toggle(s.children().length>0)}e(".button-next").on("click",function(){var t=e(this).parents("form").get(0);return("function"!=typeof t.checkValidity||t.checkValidity())&&i(),!0}),e("form.address-step").on("submit",function(t){var s=e(this);return("function"!=typeof s.checkValidity||s.checkValidity())&&i(),t.preventDefault(),e(".wc-setup-content").unblock(),e(this).WCBackboneModal({template:"wc-modal-tracking-setup"}),e(document.body).on("wc_backbone_modal_response",function(){s.off("submit").trigger("submit")}),e("#wc_tracker_checkbox_dialog").on("change",function(i){var t=e(i.target);e("#wc_tracker_checkbox").prop("checked",t.prop("checked"))}),e("#wc_tracker_submit").on("click",function(){s.off("submit").trigger("submit")}),!0}),e("#store_country").on("change",function(){if(null!==wc_setup_params.states){var i=e(this).val(),t=e("#store_state");if(e.isEmptyObject(wc_setup_params.states[i]))e(".store-state-container").hide(),t.empty().val("").trigger("change").prop("required",!1);else{var s=wc_setup_params.states[i];t.empty(),e.each(s,function(i){t.append(e('<option value="'+i+'">'+s[i]+"</option>"))}),e(".store-state-container").show(),t.selectWoo().val(wc_base_state).trigger("change").prop("required",!0)}e("#currency_code").val(wc_setup_currencies[i]).trigger("change")}}),e("#store_country").on("change",function(){if(wc_setup_params.postcodes){var i=e(this).val(),t=e("#store_postcode"),s=wc_setup_params.postcodes[i];e.isEmptyObject(s)||s.required?t.attr("required","true"):t.prop("required",!1)}}),e("#store_country").trigger("change"),e(".wc-wizard-services").on("change",".wc-wizard-service-enable input",function(){e(this).is(":checked")?(e(this).closest(".wc-wizard-service-toggle").removeClass("disabled"),e(this).closest(".wc-wizard-service-item").addClass("checked"),e(this).closest(".wc-wizard-service-item").find(".wc-wizard-service-settings").removeClass("hide")):(e(this).closest(".wc-wizard-service-toggle").addClass("disabled"),e(this).closest(".wc-wizard-service-item").removeClass("checked"),e(this).closest(".wc-wizard-service-item").find(".wc-wizard-service-settings").addClass("hide"))}),e(".wc-wizard-services").on("keyup",function(i){var t=i.keyCode||i.which,s=e(document.activeElement);!s.is(".wc-wizard-service-toggle, .wc-wizard-service-enable")||13!==t&&32!==t||s.find(":input").trigger("click")}),e(".wc-wizard-services").on("click",".wc-wizard-service-enable",function(i){if(e(i.target).is("input"))i.stopPropagation();else{var t=e(this).find('input[type="checkbox"]');t.prop("checked",!t.prop("checked")).trigger("change")}}),e(".wc-wizard-services-list-toggle").on("click",function(){var i=e(this).closest(".wc-wizard-services-list-toggle");i.hasClass("closed")?i.removeClass("closed"):i.addClass("closed"),e(this).closest(".wc-wizard-services").find(".wc-wizard-service-item").slideToggle().css("display","flex")}),e(".wc-wizard-services").on("change",".wc-wizard-shipping-method-select .method",function(i){var t=e(this).closest(".wc-wizard-service-description"),s=i.target.value,c=t.find(".shipping-method-descriptions");c.find(".shipping-method-description").addClass("hide"),c.find("."+s).removeClass("hide");var r=t.parent().find('input[type="checkbox"]'),n=t.find(".shipping-method-settings");n.find(".shipping-method-setting").addClass("hide").find(".shipping-method-required-field").prop("required",!1),n.find("."+s).removeClass("hide").find(".shipping-method-required-field").prop("required",r.prop("checked"))}).find(".wc-wizard-shipping-method-select .method").trigger("change"),e(".wc-wizard-services").on("change",".wc-wizard-shipping-method-enable",function(){var i=e(this).is(":checked"),t=e(this).closest(".wc-wizard-service-item").find(".wc-wizard-shipping-method-select .method").val();e(this).closest(".wc-wizard-service-item").find("."+t).find(".shipping-method-required-field").prop("required",i)}),e(".activate-jetpack").on("click",".button-primary",function(e){if(i(),"no"===wc_setup_params.pending_jetpack_install)return!0;e.preventDefault(),s()}),e(".activate-new-onboarding").on("click",".button-primary",function(){i()}),e(".wc-wizard-services").on("change","input#stripe_create_account, input#ppec_paypal_reroute_requests",function(){e(this).is(":checked")?e(this).closest(".wc-wizard-service-settings").find("input.payment-email-input").attr("type","email").prop("disabled",!1).prop("required",!0):e(this).closest(".wc-wizard-service-settings").find("input.payment-email-input").attr("type",null).prop("disabled",!0).prop("required",!1)}).find("input#stripe_create_account, input#ppec_paypal_reroute_requests").trigger("change"),r(),e(".wc-setup-content").on("change","[data-plugins]",r),e(document.body).on("init_tooltips",function(){e(".help_tip").tipTip({attribute:"data-tip",fadeIn:50,fadeOut:50,delay:200,defaultPosition:"top"})}).trigger("init_tooltips")});
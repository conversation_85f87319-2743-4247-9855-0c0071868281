jQuery(function(t){t("table.widefat tbody th, table.widefat tbody td").css("cursor","move"),t("table.widefat tbody").sortable({items:"tr:not(.inline-edit-row)",cursor:"move",axis:"y",containment:"table.widefat",scrollSensitivity:40,helper:function(e,i){return i.each(function(){t(this).width(t(this).width())}),i},start:function(t,e){e.item.css("background-color","#ffffff"),e.item.children("td, th").css("border-bottom-width","0"),e.item.css("outline","1px solid #dfdfdf")},stop:function(t,e){e.item.removeAttr("style"),e.item.children("td,th").css("border-bottom-width","1px")},update:function(e,i){t("table.widefat tbody th, table.widefat tbody td").css("cursor","default"),t("table.widefat tbody").sortable("disable");var o=i.item.find(".check-column input").val(),n=i.item.prev().find(".check-column input").val(),d=i.item.next().find(".check-column input").val();i.item.find(".check-column input").hide().after('<img alt="processing" src="images/wpspin_light.gif" class="waiting" style="margin-left: 6px;" />'),t.post(ajaxurl,{action:"woocommerce_product_ordering",id:o,previd:n,nextid:d},function(e){t.each(e,function(e,i){t("#inline_"+e+" .menu_order").html(i)}),i.item.find(".check-column input").show().siblings("img").remove(),t("table.widefat tbody th, table.widefat tbody td").css("cursor","move"),t("table.widefat tbody").sortable("enable")}),t("table.widefat tbody tr").each(function(){t("table.widefat tbody tr").index(this)%2==0?t(this).addClass("alternate"):t(this).removeClass("alternate")})},sort:function(e,i){i.placeholder.find("td").each(function(e,o){i.helper.find("td").eq(e).is(":visible")?t(this).show():t(this).hide()})}})});
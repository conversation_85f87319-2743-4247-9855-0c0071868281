!function(o,e){var r=function(o){this.$form=o,this.xhr=!1,this.$form.find(".woocommerce-exporter-progress").val(0),this.processStep=this.processStep.bind(this),o.on("submit",{productExportForm:this},this.onSubmit),o.find(".woocommerce-exporter-types").on("change",{productExportForm:this},this.exportTypeFields)};r.prototype.onSubmit=function(e){e.preventDefault();var r=new Date,t="wc-product-export-"+r.getDate()+"-"+(r.getMonth()+1)+"-"+r.getFullYear()+"-"+r.getTime()+".csv";e.data.productExportForm.$form.addClass("woocommerce-exporter__exporting"),e.data.productExportForm.$form.find(".woocommerce-exporter-progress").val(0),e.data.productExportForm.$form.find(".woocommerce-exporter-button").prop("disabled",!0),e.data.productExportForm.processStep(1,o(this).serialize(),"",t)},r.prototype.processStep=function(r,t,c,p){var a=this,s=o(".woocommerce-exporter-columns").val(),m=o("#woocommerce-exporter-meta:checked").length?1:0,n=o(".woocommerce-exporter-types").val(),i=o(".woocommerce-exporter-category").val(),d=a.$form.find('input[name="product_ids"]').val()||"";o.ajax({type:"POST",url:ajaxurl,data:{form:t,action:"woocommerce_do_ajax_product_export",step:r,columns:c,selected_columns:s,export_meta:m,export_types:n,export_category:i,export_product_ids:d,filename:p,security:wc_product_export_params.export_nonce},dataType:"json",success:function(o){o.success&&("done"===o.data.step?(a.$form.find(".woocommerce-exporter-progress").val(o.data.percentage),e.location=o.data.url,setTimeout(function(){a.$form.removeClass("woocommerce-exporter__exporting"),a.$form.find(".woocommerce-exporter-button").prop("disabled",!1)},2e3)):(a.$form.find(".woocommerce-exporter-progress").val(o.data.percentage),a.processStep(parseInt(o.data.step,10),t,o.data.columns,p)))}}).fail(function(o){e.console.log(o)})},r.prototype.exportTypeFields=function(){var e=o(".woocommerce-exporter-category");-1!==o.inArray("variation",o(this).val())?(e.closest("tr").hide(),e.val("").trigger("change")):e.closest("tr").show()},o.fn.wc_product_export_form=function(){return new r(this),this},o(".woocommerce-exporter").wc_product_export_form()}(jQuery,window);
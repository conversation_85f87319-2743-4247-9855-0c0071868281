!function(o){o(function(){window.wcTracks.recordEvent("wcadmin_status_widget_view")});var i=function(o){window.wcTracks.recordEvent("status_widget_click",{link:o})};o(".sales-this-month a").on("click",function(){i("net-sales")}),o(".best-seller-this-month a").on("click",function(){i("best-seller-this-month")}),o(".processing-orders a").on("click",function(){i("orders-processing")}),o(".on-hold-orders a").on("click",function(){i("orders-on-hold")}),o(".low-in-stock a").on("click",function(){i("low-stock")}),o(".out-of-stock a").on("click",function(){i("out-of-stock")}),o(".wc_sparkline.bars").each(function(){const i=[{data:o(this).data("sparkline"),color:o(this).data("color"),bars:{fillColor:o(this).data("color"),fill:!0,show:!0,lineWidth:1,barWidth:o(this).data("barwidth"),align:"center"},shadowSize:0}];o.plot(o(this),i,{grid:{show:!1}})}),o(".wc_sparkline.lines").each(function(){const i=[{data:o(this).data("sparkline"),color:o(this).data("color"),lines:{fill:!1,show:!0,lineWidth:1,align:"center"},shadowSize:0}];o.plot(o(this),i,{grid:{show:!1}})})}(jQuery);
!function(e,i,n,t){e(function(){if(document.getElementById("tmpl-wc-shipping-class-row")&&document.getElementById("tmpl-wc-shipping-class-row-blank")){var s=e(".wc-shipping-class-rows"),a=(e(".wc-shipping-class-save"),n.template("wc-shipping-class-row")),o=n.template("wc-shipping-class-row-blank"),c=Backbone.Model.extend({save:function(n){e.post(t+(t.indexOf("?")>0?"&":"?")+"action=woocommerce_shipping_classes_save_changes",{wc_shipping_classes_nonce:i.wc_shipping_classes_nonce,changes:n},this.onSaveResponse,"json")},onSaveResponse:function(e,n){"success"===n&&(e.success?(l.set("classes",e.data.shipping_classes),l.trigger("saved:classes")):e.data?window.alert(e.data):window.alert(i.strings.save_failed)),r.unblock()}}),d=Backbone.View.extend({rowTemplate:a,initialize:function(){this.listenTo(this.model,"saved:classes",this.render),e(document.body).on("click",".wc-shipping-class-add-new",{view:this},this.configureNewShippingClass),e(document.body).on("wc_backbone_modal_response",{view:this},this.onConfigureShippingClassSubmitted),e(document.body).on("wc_backbone_modal_loaded",{view:this},this.onLoadBackboneModal),e(document.body).on("wc_backbone_modal_validation",this.validateFormArguments)},block:function(){e(this.el).block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},unblock:function(){e(this.el).unblock()},render:function(){var i=_.indexBy(this.model.get("classes"),"term_id"),n=this;this.$el.empty(),this.unblock(),_.size(i)?(i=_.sortBy(i,function(e){return e.name}),e.each(i,function(e,i){n.renderRow(i)})):n.$el.append(o)},renderRow:function(e){this.$el.append(this.rowTemplate(e)),this.initRow(e)},initRow:function(i){var n=this.$el.find('tr[data-id="'+i.term_id+'"]');n.find("select").each(function(){var n=e(this).data("attribute");e(this).find('option[value="'+i[n]+'"]').prop("selected",!0)}),n.find(".view").show(),n.find(".edit").hide(),n.find(".wc-shipping-class-edit").on("click",{view:this},this.onEditRow),n.find(".wc-shipping-class-delete").on("click",{view:this},this.onDeleteRow)},configureNewShippingClass:function(i){i.preventDefault();const n="new-1-"+Date.now();e(this).WCBackboneModal({template:"wc-shipping-class-configure",variable:{term_id:n,action:"create"},data:{term_id:n,action:"create"}})},onConfigureShippingClassSubmitted:function(e,i,n){if("wc-shipping-class-configure"===i){const i=e.data.view,t=i.model,s=n.term_id.includes("new-1-"),a=Object.assign({},n);s&&(a.newRow=!0),i.block(),t.save({[n.term_id]:a})}},validateFormArguments:function(e,i,n){const t=["name","description"],s=Object.keys(n).every(e=>!t.includes(e)||(Array.isArray(n[e])?n[e].length&&!!n[e][0]:!!n[e])),a=document.getElementById("btn-ok");a.disabled=!s,a.classList.toggle("disabled",!s)},onEditRow:function(i){const n=e(this).closest("tr").data("id"),t=i.data.view.model,s=_.indexBy(t.get("classes"),"term_id")[n];i.preventDefault(),e(this).WCBackboneModal({template:"wc-shipping-class-configure",variable:Object.assign({action:"edit"},s),data:Object.assign({action:"edit"},s)})},onLoadBackboneModal:function(i,n){if("wc-shipping-class-configure"===n){const n=e(".wc-backbone-modal-content").data("id"),t=i.data.view.model,s=_.indexBy(t.get("classes"),"term_id")[n];s&&e(".wc-backbone-modal-content").find("select").each(function(){var i=e(this).data("attribute");e(this).find('option[value="'+s[i]+'"]').prop("selected",!0)})}},onDeleteRow:function(i){var n=i.data.view,t=n.model,s=e(this).closest("tr").data("id");i.preventDefault(),n.block(),t.save({[s]:{term_id:s,deleted:"deleted"}})}}),l=new c({classes:i.classes}),r=new d({model:l,el:s});r.render()}})}(jQuery,shippingClassesLocalizeScript,wp,ajaxurl);
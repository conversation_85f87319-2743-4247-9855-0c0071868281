jQuery(function(e){e("#the-list").on("click",".editinline",function(){inlineEditPost.revert();var t=e(this).closest("tr").attr("id");t=t.replace("post-","");var i=e("#woocommerce_inline_"+t),n=i.find(".sku").text(),o=i.find(".regular_price").text(),l=i.find(".sale_price ").text(),d=i.find(".weight").text(),s=i.find(".length").text(),c=i.find(".width").text(),a=i.find(".height").text(),r=i.find(".shipping_class").text(),_=i.find(".visibility").text(),m=i.find(".stock_status").text(),p=i.find(".stock").text(),u=i.find(".featured").text(),f=i.find(".manage_stock").text(),w=i.find(".menu_order").text(),h=i.find(".tax_status").text(),k=i.find(".tax_class").text(),v=i.find(".backorders").text(),g=i.find(".product_type").text(),x=o.replace(".",woocommerce_admin.mon_decimal_point),y=l.replace(".",woocommerce_admin.mon_decimal_point),b=i.find(".cogs_value ");if(b.length>0){var A=b.text().replace(".",woocommerce_admin.mon_decimal_point);e('input[name="_cogs_value"]',".inline-edit-row").val(A)}e('input[name="_sku"]',".inline-edit-row").val(n),e('input[name="_regular_price"]',".inline-edit-row").val(x),e('input[name="_sale_price"]',".inline-edit-row").val(y),e('input[name="_weight"]',".inline-edit-row").val(d),e('input[name="_length"]',".inline-edit-row").val(s),e('input[name="_width"]',".inline-edit-row").val(c),e('input[name="_height"]',".inline-edit-row").val(a),e('select[name="_shipping_class"] option:selected',".inline-edit-row").attr("selected",!1).trigger("change"),e('select[name="_shipping_class"] option[value="'+r+'"]').attr("selected","selected").trigger("change"),e('input[name="_stock"]',".inline-edit-row").val(p),e('input[name="menu_order"]',".inline-edit-row").val(w),e('select[name="_tax_status"] option, select[name="_tax_class"] option, select[name="_visibility"] option, select[name="_stock_status"] option, select[name="_backorders"] option').prop("selected",!1).removeAttr("selected");var q="variable"===g;e('select[name="_stock_status"] ~ .wc-quick-edit-warning',".inline-edit-row").toggle(q),e('select[name="_stock_status"] option[value="'+(q?"":m)+'"]',".inline-edit-row").attr("selected","selected"),e('select[name="_tax_status"] option[value="'+h+'"]',".inline-edit-row").attr("selected","selected"),e('select[name="_tax_class"] option[value="'+k+'"]',".inline-edit-row").attr("selected","selected"),e('select[name="_visibility"] option[value="'+_+'"]',".inline-edit-row").attr("selected","selected"),e('select[name="_backorders"] option[value="'+v+'"]',".inline-edit-row").attr("selected","selected"),"yes"===u?e('input[name="_featured"]',".inline-edit-row").prop("checked",!0):e('input[name="_featured"]',".inline-edit-row").prop("checked",!1);var j=i.find(".product_is_virtual").text(),E="external"!==g,I="external"!==g&&"grouped"!==g;e(".stock_fields, .manage_stock_field, .stock_status_field, .backorder_field").show(),I?"yes"===f?(e(".stock_qty_field, .backorder_field",".inline-edit-row").show().removeAttr("style"),e(".stock_status_field").hide(),e(".manage_stock_field input").prop("checked",!0)):(e(".stock_qty_field, .backorder_field",".inline-edit-row").hide(),e(".stock_status_field").show().removeAttr("style"),e(".manage_stock_field input").prop("checked",!1)):E?e(".stock_fields, .manage_stock_field, .backorder_field").hide():e(".stock_fields, .manage_stock_field, .stock_status_field, .backorder_field").hide(),"simple"===g||"external"===g?e(".price_fields",".inline-edit-row").show().removeAttr("style"):e(".price_fields",".inline-edit-row").hide(),"yes"===j?e(".dimension_fields",".inline-edit-row").hide():e(".dimension_fields",".inline-edit-row").show().removeAttr("style"),e('input[name="comment_status"]').parent().find(".checkbox-title").text(woocommerce_quick_edit.strings.allow_reviews)}),e("#the-list").on("change",'.inline-edit-row input[name="_manage_stock"]',function(){e(this).is(":checked")?(e(".stock_qty_field, .backorder_field",".inline-edit-row").show().removeAttr("style"),e(".stock_status_field").hide()):(e(".stock_qty_field, .backorder_field",".inline-edit-row").hide(),e(".stock_status_field").show().removeAttr("style"))}),e("#wpbody").on("click","#doaction, #doaction2",function(){e("input.text",".inline-edit-row").val(""),e("#woocommerce-fields").find("select").prop("selectedIndex",0),e("#woocommerce-fields-bulk").find(".inline-edit-group .change-input").hide()}),e("#wpbody").on("change","#woocommerce-fields-bulk .inline-edit-group .change_to",function(){0<e(this).val()?e(this).closest("div").find(".change-input").show():e(this).closest("div").find(".change-input").hide()}),e("#wpbody").on("click",".trash-product",function(){return window.confirm(woocommerce_admin.i18n_delete_product_notice)})});
!function(e,n,i,o){e(function(){if(document.getElementById("tmpl-wc-shipping-zone-row")&&document.getElementById("tmpl-wc-shipping-zone-row-blank")){e(".wc-shipping-zones");var t=e(".wc-shipping-zone-rows"),s=e(".wc-shipping-zone-save"),a=i.template("wc-shipping-zone-row"),d=i.template("wc-shipping-zone-row-blank"),r=Backbone.Model.extend({changes:{},logChanges:function(e){var n=this.changes||{};_.each(e,function(e,i){n[i]=_.extend(n[i]||{zone_id:i},e)}),this.changes=n,this.trigger("change:zones")},discardChanges:function(e){var n=this.changes||{},i=null,o=_.indexBy(this.get("zones"),"zone_id");n[e]&&n[e].zone_order!==undefined&&(i=n[e].zone_order),delete n[e],null!==i&&o[e]&&o[e].zone_order!==i&&(n[e]=_.extend(n[e]||{},{zone_id:e,zone_order:i})),this.changes=n,0===_.size(this.changes)&&h.clearUnloadConfirmation()},save:function(){_.size(this.changes)?e.post(o+(o.indexOf("?")>0?"&":"?")+"action=woocommerce_shipping_zones_save_changes",{wc_shipping_zones_nonce:n.wc_shipping_zones_nonce,changes:this.changes},this.onSaveResponse,"json"):c.trigger("saved:zones")},onSaveResponse:function(e,i){"success"===i&&(e.success?(c.set("zones",e.data.zones),c.trigger("change:zones"),c.changes={},c.trigger("saved:zones")):window.alert(n.strings.save_failed))}}),l=Backbone.View.extend({rowTemplate:a,initialize:function(){this.listenTo(this.model,"change:zones",this.setUnloadConfirmation),this.listenTo(this.model,"saved:zones",this.clearUnloadConfirmation),this.listenTo(this.model,"saved:zones",this.render),t.on("change",{view:this},this.updateModelOnChange),t.on("sortupdate",{view:this},this.updateModelOnSort),e(window).on("beforeunload",{view:this},this.unloadConfirmation),e(document.body).on("click",".wc-shipping-zone-add",{view:this},this.onAddNewRow)},onAddNewRow:function(){var n=e(this);window.location.href=n.attr("href")},block:function(){e(this.el).block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},unblock:function(){e(this.el).unblock()},render:function(){var n=_.indexBy(this.model.get("zones"),"zone_id"),i=this;i.$el.empty(),i.unblock(),_.size(n)?(n=_(n).chain().sortBy(function(e){return parseInt(e.zone_id,10)}).sortBy(function(e){return parseInt(e.zone_order,10)}).value(),e.each(n,function(e,n){i.renderRow(n)})):i.$el.append(d),i.initRows()},renderRow:function(e){this.$el.append(this.rowTemplate(e)),this.initRow(e)},initRow:function(e){var n=this.$el.find('tr[data-id="'+e.zone_id+'"]');this.renderShippingMethods(e.zone_id,e.shipping_methods),n.find(".wc-shipping-zone-delete").on("click",{view:this},this.onDeleteRow)},initRows:function(){const n=0!=e("tbody.wc-shipping-zone-rows tr").length%2,i=e("tfoot.wc-shipping-zone-rows-tfoot");n?i.find("tr").addClass("even"):i.find("tr").removeClass("even"),e("#tiptip_holder").removeAttr("style"),e("#tiptip_arrow").removeAttr("style"),e(".tips").tipTip({attribute:"data-tip",fadeIn:50,fadeOut:50,delay:50})},renderShippingMethods:function(i,o){var t=e('.wc-shipping-zones tr[data-id="'+i+'"]').find(".wc-shipping-zone-methods ul");t.find(".wc-shipping-zone-method").remove(),_.size(o)?(o=_.sortBy(o,function(e){return parseInt(e.method_order,10)}),_.each(o,function(e){var n="method_disabled";"yes"===e.enabled&&(n="method_enabled"),t.append('<li data-id="'+e.instance_id+'" class="wc-shipping-zone-method '+n+'">'+e.title+"</li>")})):t.append('<li class="wc-shipping-zone-method">'+n.strings.no_shipping_methods_offered+"</li>")},onDeleteRow:function(i){var o=i.data.view.model,t=_.indexBy(o.get("zones"),"zone_id"),s={},a=e(this).closest("tr").data("id");i.preventDefault(),window.confirm(n.strings.delete_confirmation_msg)&&t[a]&&(delete t[a],s[a]=_.extend(s[a]||{},{deleted:"deleted"}),o.set("zones",t),o.logChanges(s),i.data.view.block(),i.data.view.model.save())},setUnloadConfirmation:function(){this.needsUnloadConfirm=!0,s.prop("disabled",!1)},clearUnloadConfirmation:function(){this.needsUnloadConfirm=!1,s.prop("disabled",!0)},unloadConfirmation:function(e){if(e.data.view.needsUnloadConfirm)return e.returnValue=n.strings.unload_confirmation_msg,window.event.returnValue=n.strings.unload_confirmation_msg,n.strings.unload_confirmation_msg},updateModelOnChange:function(n){var i=n.data.view.model,o=e(n.target),t=o.closest("tr").data("id"),s=o.data("attribute"),a=o.val(),d=_.indexBy(i.get("zones"),"zone_id"),r={};d[t]&&d[t][s]===a||(r[t]={},r[t][s]=a),i.logChanges(r)},updateModelOnSort:function(n){var i=n.data.view.model,o=_.indexBy(i.get("zones"),"zone_id"),t=e("tbody.wc-shipping-zone-rows tr"),s={};_.each(t,function(n){var i=e(n).data("id"),t=null,a=parseInt(e(n).index(),10);o[i]&&(t=parseInt(o[i].zone_order,10)),t!==a&&(s[i]=_.extend(s[i]||{},{zone_order:a}))}),_.size(s)&&(i.logChanges(s),n.data.view.block(),n.data.view.model.save())}}),c=new r({zones:n.zones}),h=new l({model:c,el:t});h.render(),t.sortable({items:"tr",cursor:"move",axis:"y",handle:"td.wc-shipping-zone-sort",scrollSensitivity:40})}})}(jQuery,shippingZonesLocalizeScript,wp,ajaxurl);
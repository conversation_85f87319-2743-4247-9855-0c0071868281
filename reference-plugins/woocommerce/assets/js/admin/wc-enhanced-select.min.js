jQuery(function(e){function t(){return{language:{errorLoading:function(){return wc_enhanced_select_params.i18n_searching},inputTooLong:function(e){var t=e.input.length-e.maximum;return 1===t?wc_enhanced_select_params.i18n_input_too_long_1:wc_enhanced_select_params.i18n_input_too_long_n.replace("%qty%",t)},inputTooShort:function(e){var t=e.minimum-e.input.length;return 1===t?wc_enhanced_select_params.i18n_input_too_short_1:wc_enhanced_select_params.i18n_input_too_short_n.replace("%qty%",t)},loadingMore:function(){return wc_enhanced_select_params.i18n_load_more},maximumSelected:function(e){return 1===e.maximum?wc_enhanced_select_params.i18n_selection_too_long_1:wc_enhanced_select_params.i18n_selection_too_long_n.replace("%qty%",e.maximum)},noResults:function(){return wc_enhanced_select_params.i18n_no_matches},searching:function(){return wc_enhanced_select_params.i18n_searching}}}}try{e(document.body).on("wc-enhanced-select-init",function(){e(":input.wc-enhanced-select, :input.chosen_select").filter(":not(.enhanced)").each(function(){var a=e.extend({minimumResultsForSearch:10,allowClear:!!e(this).data("allow_clear"),placeholder:e(this).data("placeholder")},t());e(this).selectWoo(a).addClass("enhanced")}),e(":input.wc-enhanced-select-nostd, :input.chosen_select_nostd").filter(":not(.enhanced)").each(function(){var a=e.extend({minimumResultsForSearch:10,allowClear:!0,placeholder:e(this).data("placeholder")},t());e(this).selectWoo(a).addClass("enhanced")}),e(":input.wc-product-search").filter(":not(.enhanced)").each(function(){!function(a,n){if(n=e.extend(n,t()),e(a).selectWoo(n).addClass("enhanced"),e(a).data("sortable")){var c=e(a),i=e(a).next(".select2-container").find("ul.select2-selection__rendered");i.sortable({placeholder:"ui-state-highlight select2-selection__choice",forcePlaceholderSize:!0,items:"li:not(.select2-search__field)",tolerance:"pointer",stop:function(){e(i.find(".select2-selection__choice").get().reverse()).each(function(){var t=e(this).data("data").id,a=c.find('option[value="'+t+'"]')[0];c.prepend(a)})}})}else e(a).prop("multiple")&&e(a).on("change",function(){var t=e(a).children();t.sort(function(e,t){var a=e.text.toLowerCase(),n=t.text.toLowerCase();return a>n?1:a<n?-1:0}),e(a).html(t)})}(this,{allowClear:!!e(this).data("allow_clear"),placeholder:e(this).data("placeholder"),minimumInputLength:e(this).data("minimum_input_length")?e(this).data("minimum_input_length"):"3",escapeMarkup:function(e){return e},ajax:{url:wc_enhanced_select_params.ajax_url,dataType:"json",delay:250,data:function(t){return{term:t.term,action:e(this).data("action")||"woocommerce_json_search_products_and_variations",security:wc_enhanced_select_params.search_products_nonce,exclude:e(this).data("exclude"),exclude_type:e(this).data("exclude_type"),include:e(this).data("include"),limit:e(this).data("limit"),display_stock:e(this).data("display_stock")}},processResults:function(t){var a=[];return t&&e.each(t,function(e,t){a.push({id:e,text:t})}),{results:a}},cache:!0}})}),e(":input.wc-page-search").filter(":not(.enhanced)").each(function(){var t={allowClear:!!e(this).data("allow_clear"),placeholder:e(this).data("placeholder"),minimumInputLength:e(this).data("minimum_input_length")?e(this).data("minimum_input_length"):"3",escapeMarkup:function(e){return e},ajax:{url:wc_enhanced_select_params.ajax_url,dataType:"json",delay:250,data:function(t){return{term:t.term,action:e(this).data("action")||"woocommerce_json_search_pages",security:wc_enhanced_select_params.search_pages_nonce,exclude:e(this).data("exclude"),post_status:e(this).data("post_status"),limit:e(this).data("limit")}},processResults:function(t){var a=[];return t&&e.each(t,function(e,t){a.push({id:e,text:t})}),{results:a}},cache:!0}};e(this).selectWoo(t).addClass("enhanced")}),e(":input.wc-customer-search").filter(":not(.enhanced)").each(function(){var a={allowClear:!!e(this).data("allow_clear"),placeholder:e(this).data("placeholder"),minimumInputLength:e(this).data("minimum_input_length")?e(this).data("minimum_input_length"):"1",escapeMarkup:function(e){return e},ajax:{url:wc_enhanced_select_params.ajax_url,dataType:"json",delay:1e3,data:function(t){return{term:t.term,action:"woocommerce_json_search_customers",security:wc_enhanced_select_params.search_customers_nonce,exclude:e(this).data("exclude")}},processResults:function(t){var a=[];return t&&e.each(t,function(e,t){a.push({id:e,text:t})}),{results:a}},cache:!0}};if(a=e.extend(a,t()),e(this).selectWoo(a).addClass("enhanced"),e(this).data("sortable")){var n=e(this),c=e(this).next(".select2-container").find("ul.select2-selection__rendered");c.sortable({placeholder:"ui-state-highlight select2-selection__choice",forcePlaceholderSize:!0,items:"li:not(.select2-search__field)",tolerance:"pointer",stop:function(){e(c.find(".select2-selection__choice").get().reverse()).each(function(){var t=e(this).data("data").id,a=n.find('option[value="'+t+'"]')[0];n.prepend(a)})}})}}),e(":input.wc-category-search").filter(":not(.enhanced)").each(function(){var a=e(this).data("return_id")?"id":"slug",n=e.extend({allowClear:!!e(this).data("allow_clear"),placeholder:e(this).data("placeholder"),minimumInputLength:e(this).data("minimum_input_length")?e(this).data("minimum_input_length"):"3",escapeMarkup:function(e){return e},ajax:{url:wc_enhanced_select_params.ajax_url,dataType:"json",delay:250,data:function(e){return{term:e.term,action:"woocommerce_json_search_categories",security:wc_enhanced_select_params.search_categories_nonce}},processResults:function(t){var n=[];return t&&e.each(t,function(e,t){n.push({id:"id"===a?t.term_id:t.slug,text:t.formatted_name})}),{results:n}},cache:!0}},t());e(this).selectWoo(n).addClass("enhanced")}),e(":input.wc-taxonomy-term-search").filter(":not(.enhanced)").each(function(){var a=e(this).data("return_id")?"id":"slug",n=e.extend({allowClear:!!e(this).data("allow_clear"),placeholder:e(this).data("placeholder"),minimumInputLength:null!==e(this).data("minimum_input_length")&&e(this).data("minimum_input_length")!==undefined?e(this).data("minimum_input_length"):"3",escapeMarkup:function(e){return e},ajax:{url:wc_enhanced_select_params.ajax_url,dataType:"json",delay:250,data:function(t){return{taxonomy:e(this).data("taxonomy"),limit:e(this).data("limit"),orderby:e(this).data("orderby"),term:t.term,action:"woocommerce_json_search_taxonomy_terms",security:wc_enhanced_select_params.search_taxonomy_terms_nonce}},processResults:function(t){var n=[];return t&&e.each(t,function(e,t){n.push({id:"id"===a?t.term_id:t.slug,text:t.name})}),{results:n}},cache:!0}},t());e(this).selectWoo(n).addClass("enhanced")}),e(":input.wc-attribute-search").filter(":not(.enhanced)").each(function(){var a=this,n=e.extend({allowClear:!!e(this).data("allow_clear"),placeholder:e(this).data("placeholder"),minimumInputLength:null!==e(this).data("minimum_input_length")&&e(this).data("minimum_input_length")!==undefined?e(this).data("minimum_input_length"):"3",escapeMarkup:function(e){return e},ajax:{url:wc_enhanced_select_params.ajax_url,dataType:"json",delay:250,data:function(e){return{term:e.term,action:"woocommerce_json_search_product_attributes",security:wc_enhanced_select_params.search_product_attributes_nonce}},processResults:function(t){var n=e(a).data("disabled-items")||[],c=[];return t&&e.each(t,function(e,t){c.push({id:t.slug,text:t.name,disabled:n.includes(t.slug)})}),{results:c}},cache:!0}},t());e(this).selectWoo(n).addClass("enhanced")})}).on("wc_backbone_modal_before_remove",function(){e(".wc-enhanced-select, :input.wc-product-search, :input.wc-customer-search").filter(".select2-hidden-accessible").selectWoo("close")}).trigger("wc-enhanced-select-init"),e("html").on("click",function(t){this===t.target&&e(".wc-enhanced-select, :input.wc-product-search, :input.wc-customer-search").filter(".select2-hidden-accessible").selectWoo("close")})}catch(a){window.console.log(a)}});
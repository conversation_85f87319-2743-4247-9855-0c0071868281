!function(t){t.fn.tipTip=function(e){var o=t.extend({activation:"hover",keepAlive:!1,maxWidth:"200px",edgeOffset:3,defaultPosition:"bottom",delay:400,fadeIn:200,fadeOut:200,attribute:"title",content:!1,enter:function(){},exit:function(){}},e);if(t("#tiptip_holder").length<=0){var n=t('<div id="tiptip_holder" style="max-width:'+o.maxWidth+';"></div>'),i=t('<div id="tiptip_content"></div>'),r=t('<div id="tiptip_arrow"></div>');t("body").append(n.html(i).prepend(r.html('<div id="tiptip_arrow_inner"></div>')))}else n=t("#tiptip_holder"),i=t("#tiptip_content"),r=t("#tiptip_arrow");return this.each(function(){var e=t(this);if(o.content)var a=o.content;else a=e.attr(o.attribute);if(""!=a){o.content||e.removeAttr(o.attribute);var f=!1;function d(){var d="function"==typeof o.content?o.content():a;if(d){o.enter.call(this),i.html(d),n.hide().css("margin","0"),n.removeAttr("class"),r.removeAttr("style");var u=parseInt(e.offset().top),p=parseInt(e.offset().left),s=parseInt(e.outerWidth()),l=parseInt(e.outerHeight()),c=n.outerWidth(),h=n.outerHeight(),_=Math.round((s-c)/2),m=Math.round((l-h)/2),v=Math.round(p+_),g=Math.round(u+l+o.edgeOffset),b="",M="",w=Math.round(c-12)/2;"bottom"==o.defaultPosition?b="_bottom":"top"==o.defaultPosition?b="_top":"left"==o.defaultPosition?b="_left":"right"==o.defaultPosition&&(b="_right");var O=_+p<parseInt(t(window).scrollLeft()),x=c+p>parseInt(t(window).width());O&&_<0||"_right"==b&&!x||"_left"==b&&p<c+o.edgeOffset+5?(b="_right",M=Math.round(h-13)/2,w=-12,v=Math.round(p+s+o.edgeOffset),g=Math.round(u+m)):(x&&_<0||"_left"==b&&!O)&&(b="_left",M=Math.round(h-13)/2,w=Math.round(c),v=Math.round(p-(c+o.edgeOffset+5)),g=Math.round(u+m));var I=u+l+o.edgeOffset+h+8>parseInt(t(window).height()+t(window).scrollTop()),A=u+l-(o.edgeOffset+h+8)<0;I||"_bottom"==b&&I||"_top"==b&&!A?("_top"==b||"_bottom"==b?b="_top":b+="_top",M=h,g=Math.round(u-(h+5+o.edgeOffset))):(A|("_top"==b&&A)||"_bottom"==b&&!I)&&("_top"==b||"_bottom"==b?b="_bottom":b+="_bottom",M=-12,g=Math.round(u+l+o.edgeOffset)),"_right_top"==b||"_left_top"==b?g+=5:"_right_bottom"!=b&&"_left_bottom"!=b||(g-=5),"_left_top"!=b&&"_left_bottom"!=b||(v+=5),r.css({"margin-left":w+"px","margin-top":M+"px"}),n.css({"margin-left":v+"px","margin-top":g+"px"}).attr("class","tip"+b),f&&clearTimeout(f),f=setTimeout(function(){n.stop(!0,!0).fadeIn(o.fadeIn)},o.delay)}}function u(){o.exit.call(this),f&&clearTimeout(f),n.fadeOut(o.fadeOut)}"hover"==o.activation?(e.on("mouseenter",function(){d()}).on("mouseleave",function(){o.keepAlive&&n.is(":hover")||u()}),o.keepAlive&&n.on("mouseenter",function(){}).on("mouseleave",function(){u()})):"focus"==o.activation?e.on("focus",function(){d()}).on("blur",function(){u()}):"click"==o.activation&&(e.on("click",function(){return d(),!1}).on("mouseenter",function(){}).on("mouseleave",function(){o.keepAlive||u()}),o.keepAlive&&n.on("mouseenter",function(){}).on("mouseleave",function(){u()}))}})}}(jQuery);
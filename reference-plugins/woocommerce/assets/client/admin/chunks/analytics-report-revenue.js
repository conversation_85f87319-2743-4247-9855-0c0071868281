"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[3151],{55737:(e,t,r)=>{r.d(t,{A:()=>v});var a=r(27723),s=r(86087),o=r(29491),n=r(38443),i=r(47143),l=r(66087),c=r(98846),d=r(40314),m=r(77374),u=r(94111),p=r(96476);function y(e,t,r={}){if(!e||0===e.length)return null;const a=e.slice(0),s=a.pop();if(s.showFilters(t,r)){const e=(0,p.flattenFilters)(s.filters),r=t[s.param]||s.defaultValue||"all";return(0,l.find)(e,{value:r})}return y(a,t,r)}function g(e){return t=>(0,n.format)(e,t)}function _(e){if(e?.data?.intervals?.length>1){const t=e.data.intervals[0].date_start,r=e.data.intervals[e.data.intervals.length-1].date_end;if((0,m.containsLeapYear)(t,r))return!0}return!1}var h=r(39793);class f extends s.Component{shouldComponentUpdate(e){return e.isRequesting!==this.props.isRequesting||e.primaryData.isRequesting!==this.props.primaryData.isRequesting||e.secondaryData.isRequesting!==this.props.secondaryData.isRequesting||!(0,l.isEqual)(e.query,this.props.query)}getItemChartData(){const{primaryData:e,selectedChart:t}=this.props;return e.data.intervals.map((function(e){const r={};return e.subtotals.segments.forEach((function(e){if(e.segment_label){const a=r[e.segment_label]?e.segment_label+" (#"+e.segment_id+")":e.segment_label;r[e.segment_id]={label:a,value:e.subtotals[t.key]||0}}})),{date:(0,n.format)("Y-m-d\\TH:i:s",e.date_start),...r}}))}getTimeChartData(){const{query:e,primaryData:t,secondaryData:r,selectedChart:a,defaultDateRange:s}=this.props,o=(0,m.getIntervalForQuery)(e,s),{primary:i,secondary:l}=(0,m.getCurrentDates)(e,s);return function(e,t,r,a,s,o,i){const l=_(e),c=_(t),d=[...e.data.intervals],u=[...t.data.intervals],p=[];for(let e=0;e<d.length;e++){const t=d[e],y=(0,n.format)("Y-m-d\\TH:i:s",t.date_start),g=`${r.label} (${r.range})`,_=t.date_start,h=t.subtotals[o]||0,f=u[e],v=`${a.label} (${a.range})`;let b=(0,m.getPreviousDate)(t.date_start,r.after,a.after,s,i).format("YYYY-MM-DD HH:mm:ss"),w=f&&f.subtotals[o]||0;if("day"===i&&l&&!c&&u?.[e]){const r=new Date(t.date_start),a=new Date(u[e].date_start);(0,m.isLeapYear)(r.getFullYear())&&1===r.getMonth()&&29===r.getDate()&&2===a.getMonth()&&1===a.getDate()&&(b="-",w=0,u.splice(e,0,u[e]))}p.push({date:y,primary:{label:g,labelDate:_,value:h},secondary:{label:v,labelDate:b,value:w}})}return p}(t,r,i,l,e.compare,a.key,o)}getTimeChartTotals(){const{primaryData:e,secondaryData:t,selectedChart:r}=this.props;return{primary:(0,l.get)(e,["data","totals",r.key],null),secondary:(0,l.get)(t,["data","totals",r.key],null)}}renderChart(e,t,r,s){const{emptySearchResults:o,filterParam:n,interactiveLegend:i,itemsLabel:l,legendPosition:u,path:p,query:y,selectedChart:_,showHeaderControls:f,primaryData:v,defaultDateRange:b}=this.props,w=(0,m.getIntervalForQuery)(y,b),R=(0,m.getAllowedIntervalsForQuery)(y,b),C=(0,m.getDateFormatsForInterval)(w,v.data.intervals.length,{type:"php"}),T=o?(0,a.__)("No data for the current search","woocommerce"):(0,a.__)("No data for the selected date range","woocommerce"),{formatAmount:D,getCurrencyConfig:S}=this.context;return(0,h.jsx)(c.Chart,{allowedIntervals:R,data:r,dateParser:"%Y-%m-%dT%H:%M:%S",emptyMessage:T,filterParam:n,interactiveLegend:i,interval:w,isRequesting:t,itemsLabel:l,legendPosition:u,legendTotals:s,mode:e,path:p,query:y,screenReaderFormat:g(C.screenReaderFormat),showHeaderControls:f,title:_.label,tooltipLabelFormat:g(C.tooltipLabelFormat),tooltipTitle:"time-comparison"===e&&_.label||null,tooltipValueFormat:(0,d.getTooltipValueFormat)(_.type,D),chartType:(0,m.getChartTypeForQuery)(y),valueType:_.type,xFormat:g(C.xFormat),x2Format:g(C.x2Format),currency:S()})}renderItemComparison(){const{isRequesting:e,primaryData:t}=this.props;if(t.isError)return(0,h.jsx)(c.AnalyticsError,{});const r=e||t.isRequesting,a=this.getItemChartData();return this.renderChart("item-comparison",r,a)}renderTimeComparison(){const{isRequesting:e,primaryData:t,secondaryData:r}=this.props;if(!t||t.isError||r.isError)return(0,h.jsx)(c.AnalyticsError,{});const a=e||t.isRequesting||r.isRequesting,s=this.getTimeChartData(),o=this.getTimeChartTotals();return this.renderChart("time-comparison",a,s,o)}render(){const{mode:e}=this.props;return"item-comparison"===e?this.renderItemComparison():this.renderTimeComparison()}}f.contextType=u.CurrencyContext,f.defaultProps={isRequesting:!1,primaryData:{data:{intervals:[]},isError:!1,isRequesting:!1},secondaryData:{data:{intervals:[]},isError:!1,isRequesting:!1}};const v=(0,o.compose)((0,i.withSelect)(((e,t)=>{const{charts:r,endpoint:a,filters:s,isRequesting:o,limitProperties:n,query:i,advancedFilters:c}=t,m=n||[a],u=y(s,i),p=(0,l.get)(u,["settings","param"]),g=t.mode||function(e,t){if(e&&t){const r=(0,l.get)(e,["settings","param"]);if(!r||Object.keys(t).includes(r))return(0,l.get)(e,["chartMode"])}return null}(u,i)||"time-comparison",{woocommerce_default_date_range:_}=e(d.settingsStore).getSetting("wc_admin","wcAdminSettings"),h={mode:g,filterParam:p,defaultDateRange:_};if(o)return h;const f=m.some((e=>i[e]&&i[e].length));if(i.search&&!f)return{...h,emptySearchResults:!0};const v=e(d.reportsStore),b=r&&r.map((e=>e.key)),w=(0,d.getReportChartData)({endpoint:a,dataType:"primary",query:i,selector:v,limitBy:m,filters:s,advancedFilters:c,defaultDateRange:_,fields:b});if("item-comparison"===g)return{...h,primaryData:w};const R=(0,d.getReportChartData)({endpoint:a,dataType:"secondary",query:i,selector:v,limitBy:m,filters:s,advancedFilters:c,defaultDateRange:_,fields:b});return{...h,primaryData:w,secondaryData:R}})))(f)},68224:(e,t,r)=>{r.d(t,{A:()=>_});var a=r(27723),s=r(86087),o=r(29491),n=r(47143),i=r(96476),l=r(98846),c=r(43577),d=r(40314),m=r(77374),u=r(83306),p=r(94111),y=r(39793);class g extends s.Component{formatVal(e,t){const{formatAmount:r,getCurrencyConfig:a}=this.context;return"currency"===t?r(e):(0,c.formatValue)(a(),t,e)}getValues(e,t){const{emptySearchResults:r,summaryData:a}=this.props,{totals:s}=a,o=s.primary?s.primary[e]:0,n=s.secondary?s.secondary[e]:0,i=r?0:o,l=r?0:n;return{delta:(0,c.calculateDelta)(i,l),prevValue:this.formatVal(l,t),value:this.formatVal(i,t)}}render(){const{charts:e,query:t,selectedChart:r,summaryData:s,endpoint:o,report:n,defaultDateRange:c}=this.props,{isError:d,isRequesting:p}=s;if(d)return(0,y.jsx)(l.AnalyticsError,{});if(p)return(0,y.jsx)(l.SummaryListPlaceholder,{numberOfItems:e.length});const{compare:g}=(0,m.getDateParamsFromQuery)(t,c);return(0,y.jsx)(l.SummaryList,{children:({onToggle:t})=>e.map((e=>{const{key:s,order:c,orderby:d,label:m,type:p,isReverseTrend:_,labelTooltipText:h}=e,f={chart:s};d&&(f.orderby=d),c&&(f.order=c);const v=(0,i.getNewPath)(f),b=r.key===s,{delta:w,prevValue:R,value:C}=this.getValues(s,p);return(0,y.jsx)(l.SummaryNumber,{delta:w,href:v,label:m,reverseTrend:_,prevLabel:"previous_period"===g?(0,a.__)("Previous period:","woocommerce"):(0,a.__)("Previous year:","woocommerce"),prevValue:R,selected:b,value:C,labelTooltipText:h,onLinkClickCallback:()=>{t&&t(),(0,u.recordEvent)("analytics_chart_tab_click",{report:n||o,key:s})}},s)}))})}}g.defaultProps={summaryData:{totals:{primary:{},secondary:{}},isError:!1}},g.contextType=p.CurrencyContext;const _=(0,o.compose)((0,n.withSelect)(((e,t)=>{const{charts:r,endpoint:a,limitProperties:s,query:o,filters:n,advancedFilters:i}=t,l=s||[a],c=l.some((e=>o[e]&&o[e].length));if(o.search&&!c)return{emptySearchResults:!0};const m=r&&r.map((e=>e.key)),{woocommerce_default_date_range:u}=e(d.settingsStore).getSetting("wc_admin","wcAdminSettings");return{summaryData:(0,d.getSummaryNumbers)({endpoint:a,query:o,select:e,limitBy:l,filters:n,advancedFilters:i,defaultDateRange:u,fields:m}),defaultDateRange:u}})))(g)},94693:(e,t,r)=>{r.d(t,{Qc:()=>n,eg:()=>o,uW:()=>l});var a=r(27723),s=r(52619);const o=(0,s.applyFilters)("woocommerce_admin_revenue_report_charts",[{key:"gross_sales",label:(0,a.__)("Gross sales","woocommerce"),order:"desc",orderby:"gross_sales",type:"currency",isReverseTrend:!1},{key:"refunds",label:(0,a.__)("Returns","woocommerce"),order:"desc",orderby:"refunds",type:"currency",isReverseTrend:!0},{key:"coupons",label:(0,a.__)("Coupons","woocommerce"),order:"desc",orderby:"coupons",type:"currency",isReverseTrend:!1},{key:"net_revenue",label:(0,a.__)("Net sales","woocommerce"),orderby:"net_revenue",type:"currency",isReverseTrend:!1,labelTooltipText:(0,a.__)("Full refunds are not deducted from tax or net sales totals","woocommerce")},{key:"taxes",label:(0,a.__)("Taxes","woocommerce"),order:"desc",orderby:"taxes",type:"currency",isReverseTrend:!1,labelTooltipText:(0,a.__)("Full refunds are not deducted from tax or net sales totals","woocommerce")},{key:"shipping",label:(0,a.__)("Shipping","woocommerce"),orderby:"shipping",type:"currency",isReverseTrend:!1},{key:"total_sales",label:(0,a.__)("Total sales","woocommerce"),order:"desc",orderby:"total_sales",type:"currency",isReverseTrend:!1}]),n=(0,s.applyFilters)("woocommerce_admin_revenue_report_advanced_filters",{filters:{},title:(0,a._x)("Revenue Matches <select/> Filters","A sentence describing filters for Revenue. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")}),i=[];Object.keys(n.filters).length&&(i.push({label:(0,a.__)("All Revenue","woocommerce"),value:"all"}),i.push({label:(0,a.__)("Advanced Filters","woocommerce"),value:"advanced"}));const l=(0,s.applyFilters)("woocommerce_admin_revenue_report_filters",[{label:(0,a.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>i.length>0,filters:i}])},95220:(e,t,r)=>{r.r(t),r.d(t,{default:()=>q});var a=r(86087),s=r(27723),o=r(94693),n=r(95272),i=r(55737),l=r(68224),c=r(38443),d=r(47143),m=r(29491),u=r(66087),p=r(98846),y=r(43577),g=r(40314),_=r(77374),h=r(4594),f=r(94111),v=r(97605),b=r(56109),w=r(39793);const R=[],C=["orders_count","gross_sales","total_sales","refunds","coupons","taxes","shipping","net_revenue"];class T extends a.Component{constructor(){super(),this.getHeadersContent=this.getHeadersContent.bind(this),this.getRowsContent=this.getRowsContent.bind(this),this.getSummary=this.getSummary.bind(this)}getHeadersContent(){return[{label:(0,s.__)("Date","woocommerce"),key:"date",required:!0,defaultSort:!0,isLeftAligned:!0,isSortable:!0},{label:(0,s.__)("Orders","woocommerce"),key:"orders_count",required:!1,isSortable:!0,isNumeric:!0},{label:(0,s.__)("Gross sales","woocommerce"),key:"gross_sales",required:!1,isSortable:!0,isNumeric:!0},{label:(0,s.__)("Returns","woocommerce"),key:"refunds",required:!1,isSortable:!0,isNumeric:!0},{label:(0,s.__)("Coupons","woocommerce"),key:"coupons",required:!1,isSortable:!0,isNumeric:!0},{label:(0,s.__)("Net sales","woocommerce"),key:"net_revenue",required:!1,isSortable:!0,isNumeric:!0},{label:(0,s.__)("Taxes","woocommerce"),key:"taxes",required:!1,isSortable:!0,isNumeric:!0},{label:(0,s.__)("Shipping","woocommerce"),key:"shipping",required:!1,isSortable:!0,isNumeric:!0},{label:(0,s.__)("Total sales","woocommerce"),key:"total_sales",required:!1,isSortable:!0,isNumeric:!0}]}getRowsContent(e=[]){const t=(0,b.Qk)("dateFormat",_.defaultTableDateFormat),{formatAmount:r,render:a,formatDecimal:s,getCurrencyConfig:o}=this.context;return e.map((e=>{const{coupons:n,gross_sales:i,total_sales:l,net_revenue:d,orders_count:m,refunds:u,shipping:g,taxes:_}=e.subtotals,h=(0,w.jsx)(p.Link,{href:`edit.php?post_type=shop_order&order_date_type=${this.props.dateType}&m=`+(0,c.format)("Ymd",e.date_start),type:"wp-admin",children:(0,y.formatValue)(o(),"number",m)});return[{display:(0,w.jsx)(p.Date,{date:e.date_start,visibleFormat:t}),value:e.date_start},{display:h,value:Number(m)},{display:a(i),value:s(i)},{display:r(u),value:s(u)},{display:r(n),value:s(n)},{display:a(d),value:s(d)},{display:a(_),value:s(_)},{display:a(g),value:s(g)},{display:a(l),value:s(l)}]}))}getSummary(e,t=0){const{orders_count:r=0,gross_sales:a=0,total_sales:o=0,refunds:n=0,coupons:i=0,taxes:l=0,shipping:c=0,net_revenue:d=0}=e,{formatAmount:m,getCurrencyConfig:u}=this.context,p=u();return[{label:(0,s._n)("day","days",t,"woocommerce"),value:(0,y.formatValue)(p,"number",t)},{label:(0,s._n)("order","orders",r,"woocommerce"),value:(0,y.formatValue)(p,"number",r)},{label:(0,s.__)("Gross sales","woocommerce"),value:m(a)},{label:(0,s.__)("Returns","woocommerce"),value:m(n)},{label:(0,s.__)("Coupons","woocommerce"),value:m(i)},{label:(0,s.__)("Net sales","woocommerce"),value:m(d)},{label:(0,s.__)("Taxes","woocommerce"),value:m(l)},{label:(0,s.__)("Shipping","woocommerce"),value:m(c)},{label:(0,s.__)("Total sales","woocommerce"),value:m(o)}]}render(){const{advancedFilters:e,filters:t,tableData:r,query:a}=this.props;return(0,w.jsx)(v.A,{endpoint:"revenue",getHeadersContent:this.getHeadersContent,getRowsContent:this.getRowsContent,getSummary:this.getSummary,summaryFields:C,query:a,tableData:r,title:(0,s.__)("Revenue","woocommerce"),columnPrefsKey:"revenue_report_columns",filters:t,advancedFilters:e})}}T.contextType=f.CurrencyContext;const D=(0,u.memoize)(((e,t,r,a,s)=>({tableData:{items:{data:(0,u.get)(a,["data","intervals"],R),totalResults:(0,u.get)(a,["totalResults"],0)},isError:e,isRequesting:t,query:r},dateType:s})),((e,t,r,a,s)=>[e,t,(0,h.stringify)(r),(0,u.get)(a,["totalResults"],0),(0,u.get)(a,["data","intervals"],R).length,s].join(":"))),S=(0,u.memoize)(((e,t,r,a,s)=>({interval:"day",orderby:t,order:e,page:r,per_page:a,after:(0,_.appendTimestamp)(s.primary.after,"start"),before:(0,_.appendTimestamp)(s.primary.before,"end")})),((e,t,r,a,s)=>[e,t,r,a,s.primary.after,s.primary.before].join(":"))),x=(0,m.compose)((0,d.withSelect)(((e,t)=>{const{query:r,filters:a,advancedFilters:s}=t,{woocommerce_default_date_range:o}=e(g.settingsStore).getSetting("wc_admin","wcAdminSettings"),{getOption:n}=e(g.optionsStore),i=n("woocommerce_date_type")||"date_paid",l=(0,_.getCurrentDates)(r,o),{getReportStats:c,getReportStatsError:d,isResolving:m}=e(g.reportsStore),u=S(r.order||"desc",r.orderby||"date",r.paged||1,r.per_page||g.QUERY_DEFAULTS.pageSize,l),p=(0,g.getReportTableQuery)({endpoint:"revenue",query:r,select:e,tableQuery:u,filters:a,advancedFilters:s}),y=c("revenue",p),h=Boolean(d("revenue",p)),f=m("getReportStats",["revenue",p]);return D(h,f,u,y,i)})))(T);var F=r(88711),k=r(84179);class q extends a.Component{render(){const{path:e,query:t}=this.props;return(0,w.jsxs)(a.Fragment,{children:[(0,w.jsx)(F.A,{query:t,path:e,report:"revenue",filters:o.uW,advancedFilters:o.Qc}),(0,w.jsx)(l.A,{charts:o.eg,endpoint:"revenue",query:t,selectedChart:(0,n.A)(t.chart,o.eg),filters:o.uW,advancedFilters:o.Qc}),(0,w.jsx)(i.A,{charts:o.eg,endpoint:"revenue",path:e,query:t,selectedChart:(0,n.A)(t.chart,o.eg),filters:o.uW,advancedFilters:o.Qc}),(0,w.jsx)(x,{query:t,filters:o.uW,advancedFilters:o.Qc}),(0,w.jsx)(k.F,{optionName:"woocommerce_revenue_report_date_tour_shown",headingText:(0,s.__)("Revenue is now reported from paid orders ✅","woocommerce")})]})}}},84179:(e,t,r)=>{r.d(t,{F:()=>m});var a=r(98846),s=r(27723),o=r(40314),n=r(86087),i=r(47143),l=r(15703),c=r(39793);const d="woocommerce_date_type",m=({optionName:e,headingText:t})=>{const[r,m]=(0,n.useState)(!1),{updateOptions:u}=(0,i.useDispatch)(o.optionsStore),{shouldShowTour:p,isResolving:y}=(0,i.useSelect)((t=>{const{getOption:r,hasFinishedResolution:a}=t(o.optionsStore);return{shouldShowTour:"yes"!==r(e)&&!1===r(d),isResolving:!a("getOption",[e])||!a("getOption",[d])}}),[e]);if(r||!p||y)return null;const g={steps:[{referenceElements:{desktop:".woocommerce-filters-filter > .components-dropdown"},focusElement:{desktop:".woocommerce-filters-filter > .components-dropdown"},meta:{name:"product-feedback-",heading:t,descriptions:{desktop:(0,n.createInterpolateElement)((0,s.__)("We now collect orders in this table based on when the payment went through, rather than when they were placed. You can change this in <link>settings</link>.","woocommerce"),{link:(0,n.createElement)("a",{href:(0,l.getAdminLink)("admin.php?page=wc-admin&path=/analytics/settings"),"aria-label":(0,s.__)("Analytics date settings","woocommerce")})})},primaryButton:{text:(0,s.__)("Got it","woocommerce")}},options:{classNames:{desktop:"woocommerce-revenue-report-date-tour"}}}],closeHandler:()=>{u({[e]:"yes"}),m(!0)}};return(0,c.jsx)(a.TourKit,{config:g})}},95272:(e,t,r)=>{r.d(t,{A:()=>s});var a=r(66087);function s(e,t=[]){return(0,a.find)(t,{key:e})||t[0]}}}]);
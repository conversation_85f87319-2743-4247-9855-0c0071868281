/*! For license information please see 624.js.LICENSE.txt */
(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[624],{84437:(t,e,s)=>{"use strict";s.d(e,{d4:()=>h,zl:()=>f});var n=s(51609),o=s(94652),i=s(96796),r=s(4316),a=s(74590);const c=(t,e)=>{e(t);const s=t.getSnapshot().children;s&&Object.values(s).forEach((t=>{c(t,e)}))};function u(t,e){return t===e}function h(t,e,s=u){const o=(0,n.useCallback)((e=>{if(!t)return()=>{};const{unsubscribe:s}=t.subscribe(e);return s}),[t]),i=(0,n.useCallback)((()=>t?.getSnapshot()),[t]);return(0,a.useSyncExternalStoreWithSelector)(o,i,i,e,s)}function f(t,e={}){return function(t,e={}){const s=function(t,e){let[[s,o],a]=(0,n.useState)((()=>{const s=(0,r.A)(t,e);return[t.config,s]}));if(t.config!==s){const s=(0,r.A)(t,{...e,snapshot:o.getPersistedSnapshot({__unsafeAllowInlineActors:!0})});a([t.config,s]),o=s}return(0,i.A)((()=>{o.logic.implementations=t.implementations})),o}(t,e),a=(0,n.useCallback)((()=>s.getSnapshot()),[s]),u=(0,n.useCallback)((t=>{const{unsubscribe:e}=s.subscribe(t);return e}),[s]),h=(0,o.useSyncExternalStore)(u,a,a);return(0,n.useEffect)((()=>(s.start(),()=>{!function(t){const e=[];c(t,(t=>{e.push([t,t.getSnapshot()]),t.observers=new Set}));const s=t.system.getSnapshot?.();t.stop(),t.system._snapshot=s,e.forEach((([t,e])=>{t._processingStatus=0,t._snapshot=e}))}(s)})),[s]),[h,s.send,s]}(t,e)}},17697:(t,e)=>{var s;!function(){"use strict";var n={}.hasOwnProperty;function o(){for(var t=[],e=0;e<arguments.length;e++){var s=arguments[e];if(s){var i=typeof s;if("string"===i||"number"===i)t.push(s);else if(Array.isArray(s)){if(s.length){var r=o.apply(null,s);r&&t.push(r)}}else if("object"===i){if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]")){t.push(s.toString());continue}for(var a in s)n.call(s,a)&&s[a]&&t.push(a)}}}return t.join(" ")}t.exports?(o.default=o,t.exports=o):void 0===(s=function(){return o}.apply(e,[]))||(t.exports=s)}()},94736:(t,e,s)=>{"use strict";e.A=function(t){var e=t.size,s=void 0===e?24:e,n=t.onClick,a=(t.icon,t.className),c=function(t,e){if(null==t)return{};var s,n,o=function(t,e){if(null==t)return{};var s,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)s=i[n],0<=e.indexOf(s)||(o[s]=t[s]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)s=i[n],0<=e.indexOf(s)||Object.prototype.propertyIsEnumerable.call(t,s)&&(o[s]=t[s])}return o}(t,i),u=["gridicon","gridicons-notice-outline",a,!!function(t){return 0==t%18}(s)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return o.default.createElement("svg",r({className:u,height:s,width:s,onClick:n},c,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),o.default.createElement("g",null,o.default.createElement("path",{d:"M12 4c4.411 0 8 3.589 8 8s-3.589 8-8 8-8-3.589-8-8 3.589-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-2-2h2l.5-6h-3l.5 6z"})))};var n,o=(n=s(51609))&&n.__esModule?n:{default:n},i=["size","onClick","icon","className"];function r(){return r=Object.assign?Object.assign.bind():function(t){for(var e,s=1;s<arguments.length;s++)for(var n in e=arguments[s])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},r.apply(this,arguments)}},84343:t=>{function e(t,e){if((t=t.replace(/\s+/g,""))===(e=e.replace(/\s+/g,"")))return 1;if(t.length<2||e.length<2)return 0;let s=new Map;for(let e=0;e<t.length-1;e++){const n=t.substring(e,e+2),o=s.has(n)?s.get(n)+1:1;s.set(n,o)}let n=0;for(let t=0;t<e.length-1;t++){const o=e.substring(t,t+2),i=s.has(o)?s.get(o):0;i>0&&(s.set(o,i-1),n++)}return 2*n/(t.length+e.length-2)}t.exports={compareTwoStrings:e,findBestMatch:function(t,s){if(!function(t,e){return"string"==typeof t&&!!Array.isArray(e)&&!!e.length&&!e.find((function(t){return"string"!=typeof t}))}(t,s))throw new Error("Bad arguments: First argument should be a string, second should be an array of strings");const n=[];let o=0;for(let i=0;i<s.length;i++){const r=s[i],a=e(t,r);n.push({target:r,rating:a}),a>n[o].rating&&(o=i)}return{ratings:n,bestMatch:n[o],bestMatchIndex:o}}}},96796:(t,e,s)=>{"use strict";s.d(e,{A:()=>n});const n=s(51609).useLayoutEffect},26459:(t,e,s)=>{"use strict";var n=s(51609),o="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},i=n.useState,r=n.useEffect,a=n.useLayoutEffect,c=n.useDebugValue;function u(t){var e=t.getSnapshot;t=t.value;try{var s=e();return!o(t,s)}catch(t){return!0}}var h="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var s=e(),n=i({inst:{value:s,getSnapshot:e}}),o=n[0].inst,h=n[1];return a((function(){o.value=s,o.getSnapshot=e,u(o)&&h({inst:o})}),[t,s,e]),r((function(){return u(o)&&h({inst:o}),t((function(){u(o)&&h({inst:o})}))}),[t]),c(s),s};e.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:h},43528:(t,e,s)=>{"use strict";var n=s(51609),o=s(94652),i="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},r=o.useSyncExternalStore,a=n.useRef,c=n.useEffect,u=n.useMemo,h=n.useDebugValue;e.useSyncExternalStoreWithSelector=function(t,e,s,n,o){var f=a(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;f=u((function(){function t(t){if(!c){if(c=!0,r=t,t=n(t),void 0!==o&&d.hasValue){var e=d.value;if(o(e,t))return a=e}return a=t}if(e=a,i(r,t))return e;var s=n(t);return void 0!==o&&o(e,s)?e:(r=t,a=s)}var r,a,c=!1,u=void 0===s?null:s;return[function(){return t(e())},null===u?void 0:function(){return t(u())}]}),[e,s,n,o]);var p=r(t,f[0],f[1]);return c((function(){d.hasValue=!0,d.value=p}),[p]),h(p),p}},94652:(t,e,s)=>{"use strict";t.exports=s(26459)},74590:(t,e,s)=>{"use strict";t.exports=s(43528)},53187:(t,e,s)=>{"use strict";s.d(e,{SP:()=>i,Sx:()=>u});var n=s(4316);const o=new WeakMap;function i(t){return{config:t,start:(e,s)=>{const{self:n,system:i}=s,r={receivers:void 0,dispose:void 0};o.set(n,r),r.dispose=t({input:e.input,system:i,self:n,sendBack:t=>{"stopped"!==n.getSnapshot().status&&n._parent&&i._relay(n,n._parent,t)},receive:t=>{r.receivers??=new Set,r.receivers.add(t)}})},transition:(t,e,s)=>{const i=o.get(s.self);return e.type===n.X?(t={...t,status:"stopped",error:void 0},i.dispose?.(),t):(i.receivers?.forEach((t=>t(e))),t)},getInitialSnapshot:(t,e)=>({status:"active",output:void 0,error:void 0,input:e}),getPersistedSnapshot:t=>t,restoreSnapshot:t=>t}}const r="xstate.promise.resolve",a="xstate.promise.reject",c=new WeakMap;function u(t){return{config:t,transition:(t,e,s)=>{if("active"!==t.status)return t;switch(e.type){case r:{const s=e.data;return{...t,status:"done",output:s,input:void 0}}case a:return{...t,status:"error",error:e.data,input:void 0};case n.X:return c.get(s.self)?.abort(),{...t,status:"stopped",input:void 0};default:return t}},start:(e,{self:s,system:n})=>{if("active"!==e.status)return;const o=new AbortController;c.set(s,o),Promise.resolve(t({input:e.input,system:n,self:s,signal:o.signal})).then((t=>{"active"===s.getSnapshot().status&&(c.delete(s),n._relay(s,s,{type:r,data:t}))}),(t=>{"active"===s.getSnapshot().status&&(c.delete(s),n._relay(s,s,{type:a,data:t}))}))},getInitialSnapshot:(t,e)=>({status:"active",output:void 0,error:void 0,input:e}),getPersistedSnapshot:t=>t,restoreSnapshot:t=>t}}},46183:(t,e,s)=>{"use strict";s.d(e,{s:()=>n});const n=t=>{if("undefined"==typeof window)return;const e=function(){const t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==s.g?s.g:void 0;if(t.__xstate__)return t.__xstate__}();e&&e.register(t)}},71529:(t,e,s)=>{"use strict";s.d(e,{a:()=>r,b:()=>g,c:()=>p,s:()=>l});var n=s(4316);function o(t,{machine:e,context:s},o,i){return(r,a)=>{const c=((r,a={})=>{const{systemId:c,input:u}=a;if("string"==typeof r){const h=(0,n.z)(e,r);if(!h)throw new Error(`Actor logic '${r}' not implemented in machine '${e.id}'`);const f=(0,n.A)(h,{id:a.id,parent:t.self,syncSnapshot:a.syncSnapshot,input:"function"==typeof u?u({context:s,event:o,self:t.self}):u,src:r,systemId:c});return i[f.id]=f,f}return(0,n.A)(r,{id:a.id,parent:t.self,syncSnapshot:a.syncSnapshot,input:a.input,src:r,systemId:c})})(r,a);return i[c.id]=c,t.defer((()=>{c._processingStatus!==n.T.Stopped&&c.start()})),c}}function i(t,e,s,i,{assignment:r}){if(!e.context)throw new Error("Cannot assign to undefined `context`. Ensure that `context` is defined in the machine config.");const a={},c={context:e.context,event:s.event,spawn:o(t,e,s.event,a),self:t.self,system:t.system};let u={};if("function"==typeof r)u=r(c,i);else for(const t of Object.keys(r)){const e=r[t];u[t]="function"==typeof e?e(c,i):e}const h=Object.assign({},e.context,u);return[(0,n.U)(e,{context:h,children:Object.keys(a).length?{...e.children,...a}:e.children})]}function r(t){function e(t,e){}return e.type="xstate.assign",e.assignment=t,e.resolve=i,e}function a(t,e,s,n,{event:o}){return[e,{event:"function"==typeof o?o(s,n):o}]}function c(t,{event:e}){t.defer((()=>t.emit(e)))}let u=function(t){return t.Parent="#_parent",t.Internal="#_internal",t}({});function h(t,e,s,n,{to:o,event:i,id:r,delay:a},c){const h=e.machine.implementations.delays;if("string"==typeof i)throw new Error(`Only event objects may be used with sendTo; use sendTo({ type: "${i}" }) instead`);const f="function"==typeof i?i(s,n):i;let d;if("string"==typeof a){const t=h&&h[a];d="function"==typeof t?t(s,n):t}else d="function"==typeof a?a(s,n):a;const p="function"==typeof o?o(s,n):o;let l;if("string"==typeof p){if(l=p===u.Parent?t.self._parent:p===u.Internal?t.self:p.startsWith("#_")?e.children[p.slice(2)]:c.deferredActorIds?.includes(p)?p:e.children[p],!l)throw new Error(`Unable to send event to actor '${p}' from machine '${e.machine.id}'.`)}else l=p||t.self;return[e,{to:l,event:f,id:r,delay:d}]}function f(t,e,s){"string"==typeof s.to&&(s.to=e.children[s.to])}function d(t,e){t.defer((()=>{const{to:s,event:o,delay:i,id:r}=e;"number"!=typeof i?t.system._relay(t.self,s,o.type===n.V?(0,n.W)(t.self.id,o.data):o):t.system.scheduler.schedule(t.self,s,o,i,r)}))}function p(t,e,s){function n(t,e){}return n.type="xsnapshot.sendTo",n.to=t,n.event=e,n.id=s?.id,n.delay=s?.delay,n.resolve=h,n.retryResolve=f,n.execute=d,n}function l(t,e){return p(u.Parent,t,e)}function y(t,e,s,o,{collect:i}){const u=[],h=function(t){u.push(t)};return h.assign=(...t)=>{u.push(r(...t))},h.cancel=(...t)=>{u.push((0,n.M)(...t))},h.raise=(...t)=>{u.push((0,n.O)(...t))},h.sendTo=(...t)=>{u.push(p(...t))},h.spawnChild=(...t)=>{u.push((0,n.P)(...t))},h.stopChild=(...t)=>{u.push((0,n.R)(...t))},h.emit=(...t)=>{u.push(function(t){function e(t,e){}return e.type="xstate.emit",e.event=t,e.resolve=a,e.execute=c,e}(...t))},i({context:s.context,event:s.event,enqueue:h,check:t=>(0,n.e)(t,e.context,s.event,e),self:t.self,system:t.system},o),[e,void 0,u]}function g(t){function e(t,e){}return e.type="xstate.enqueueActions",e.collect=t,e.resolve=y,e}},4316:(t,e,s)=>{"use strict";s.d(e,{$:()=>M,A:()=>C,G:()=>G,M:()=>V,N:()=>a,O:()=>Qt,P:()=>W,R:()=>U,S:()=>i,T:()=>N,U:()=>qt,V:()=>h,W:()=>p,X:()=>f,a:()=>I,b:()=>rt,c:()=>A,d:()=>ct,e:()=>Q,f:()=>at,g:()=>it,h:()=>ot,i:()=>Y,j:()=>yt,k:()=>Wt,l:()=>st,m:()=>w,n:()=>Mt,o:()=>gt,p:()=>Tt,q:()=>l,r:()=>Ct,s:()=>kt,t:()=>k,u:()=>dt,v:()=>S,w:()=>nt,x:()=>lt,y:()=>Lt,z:()=>T});var n=s(46183);class o{constructor(t){this._process=t,this._active=!1,this._current=null,this._last=null}start(){this._active=!0,this.flush()}clear(){this._current&&(this._current.next=null,this._last=this._current)}enqueue(t){const e={value:t,next:null};if(this._current)return this._last.next=e,void(this._last=e);this._current=e,this._last=e,this._active&&this.flush()}flush(){for(;this._current;){const t=this._current;this._process(t.value),this._current=t.next}this._last=null}}const i=".",r="",a="",c="*",u="xstate.init",h="xstate.error",f="xstate.stop";function d(t,e){return{type:`xstate.done.state.${t}`,output:e}}function p(t,e){return{type:`xstate.error.actor.${t}`,error:e}}function l(t){return{type:u,input:t}}function y(t){setTimeout((()=>{throw t}))}const g="function"==typeof Symbol&&Symbol.observable||"@@observable";function v(t,e){return`${t.sessionId}.${e}`}let m=0;function _(t,e){const s=b(t),n=b(e);return"string"==typeof n?"string"==typeof s&&n===s:"string"==typeof s?s in n:Object.keys(s).every((t=>t in n&&_(s[t],n[t])))}function S(t){if($(t))return t;let e=[],s="";for(let n=0;n<t.length;n++){switch(t.charCodeAt(n)){case 92:s+=t[n+1],n++;continue;case 46:e.push(s),s="";continue}s+=t[n]}return e.push(s),e}function b(t){return(e=t)&&"object"==typeof e&&"machine"in e&&"value"in e?t.value:"string"!=typeof t?t:function(t){if(1===t.length)return t[0];const e={};let s=e;for(let e=0;e<t.length-1;e++)if(e===t.length-2)s[t[e]]=t[e+1];else{const n=s;s={},n[t[e]]=s}return e}(S(t));var e}function w(t,e){const s={},n=Object.keys(t);for(let o=0;o<n.length;o++){const i=n[o];s[i]=e(t[i],i,t,o)}return s}function x(t){return $(t)?t:[t]}function k(t){return void 0===t?[]:x(t)}function E(t,e,s,n){return"function"==typeof t?t({context:e,event:s,self:n}):t}function $(t){return Array.isArray(t)}function I(t){return x(t).map((t=>void 0===t||"string"==typeof t?{target:t}:t))}function O(t){if(void 0!==t&&t!==r)return k(t)}function j(t,e,s){const n="object"==typeof t,o=n?t:void 0;return{next:(n?t.next:t)?.bind(o),error:(n?t.error:e)?.bind(o),complete:(n?t.complete:s)?.bind(o)}}function A(t,e){return`${e}.${t}`}function T(t,e){const s=e.match(/^xstate\.invoke\.(\d+)\.(.*)/);if(!s)return t.implementations.actors[e];const[,n,o]=s,i=t.getStateNodeById(o).config.invoke;return(Array.isArray(i)?i[n]:i).src}const M=1;let N=function(t){return t[t.NotStarted=0]="NotStarted",t[t.Running=1]="Running",t[t.Stopped=2]="Stopped",t}({});const R={clock:{setTimeout:(t,e)=>setTimeout(t,e),clearTimeout:t=>clearTimeout(t)},logger:console.log.bind(console),devTools:!1};class P{constructor(t,e){this.logic=t,this._snapshot=void 0,this.clock=void 0,this.options=void 0,this.id=void 0,this.mailbox=new o(this._process.bind(this)),this.observers=new Set,this.eventListeners=new Map,this.logger=void 0,this._processingStatus=N.NotStarted,this._parent=void 0,this._syncSnapshot=void 0,this.ref=void 0,this._actorScope=void 0,this._systemId=void 0,this.sessionId=void 0,this.system=void 0,this._doneEvent=void 0,this.src=void 0,this._deferred=[];const s={...R,...e},{clock:n,logger:i,parent:r,syncSnapshot:a,id:c,systemId:u,inspect:h}=s;this.system=r?r.system:function(t,e){const s=new Map,n=new Map,o=new WeakMap,i=new Set,r={},{clock:a,logger:c}=e,u={schedule:(t,e,s,n,o=Math.random().toString(36).slice(2))=>{const i={source:t,target:e,event:s,delay:n,id:o,startedAt:Date.now()},c=v(t,o);h._snapshot._scheduledEvents[c]=i;const u=a.setTimeout((()=>{delete r[c],delete h._snapshot._scheduledEvents[c],h._relay(t,e,s)}),n);r[c]=u},cancel:(t,e)=>{const s=v(t,e),n=r[s];delete r[s],delete h._snapshot._scheduledEvents[s],a.clearTimeout(n)},cancelAll:t=>{for(const e in h._snapshot._scheduledEvents){const s=h._snapshot._scheduledEvents[e];s.source===t&&u.cancel(t,s.id)}}},h={_snapshot:{_scheduledEvents:(e?.snapshot&&e.snapshot.scheduler)??{}},_bookId:()=>"x:"+m++,_register:(t,e)=>(s.set(t,e),t),_unregister:t=>{s.delete(t.sessionId);const e=o.get(t);void 0!==e&&(n.delete(e),o.delete(t))},get:t=>n.get(t),_set:(t,e)=>{const s=n.get(t);if(s&&s!==e)throw new Error(`Actor with system ID '${t}' already exists.`);n.set(t,e),o.set(e,t)},inspect:t=>{i.add(t)},_sendInspectionEvent:e=>{if(!i.size)return;const s={...e,rootId:t.sessionId};i.forEach((t=>t.next?.(s)))},_relay:(t,e,s)=>{h._sendInspectionEvent({type:"@xstate.event",sourceRef:t,actorRef:e,event:s}),e._send(s)},scheduler:u,getSnapshot:()=>({_scheduledEvents:{...h._snapshot._scheduledEvents}}),start:()=>{const t=h._snapshot._scheduledEvents;h._snapshot._scheduledEvents={};for(const e in t){const{source:s,target:n,event:o,delay:i,id:r}=t[e];u.schedule(s,n,o,i,r)}},_clock:a,_logger:c};return h}(this,{clock:n,logger:i}),h&&!r&&this.system.inspect(j(h)),this.sessionId=this.system._bookId(),this.id=c??this.sessionId,this.logger=e?.logger??this.system._logger,this.clock=e?.clock??this.system._clock,this._parent=r,this._syncSnapshot=a,this.options=s,this.src=s.src??t,this.ref=this,this._actorScope={self:this,id:this.id,sessionId:this.sessionId,logger:this.logger,defer:t=>{this._deferred.push(t)},system:this.system,stopChild:t=>{if(t._parent!==this)throw new Error(`Cannot stop child actor ${t.id} of ${this.id} because it is not a child`);t._stop()},emit:t=>{const e=this.eventListeners.get(t.type),s=this.eventListeners.get("*");if(!e&&!s)return;const n=new Set([...e?e.values():[],...s?s.values():[]]);for(const e of Array.from(n))e(t)}},this.send=this.send.bind(this),this.system._sendInspectionEvent({type:"@xstate.actor",actorRef:this}),u&&(this._systemId=u,this.system._set(u,this)),this._initState(e?.snapshot??e?.state),u&&"active"!==this._snapshot.status&&this.system._unregister(this)}_initState(t){try{this._snapshot=t?this.logic.restoreSnapshot?this.logic.restoreSnapshot(t,this._actorScope):t:this.logic.getInitialSnapshot(this._actorScope,this.options?.input)}catch(t){this._snapshot={status:"error",output:void 0,error:t}}}update(t,e){let s;for(this._snapshot=t;s=this._deferred.shift();)try{s()}catch(e){this._deferred.length=0,this._snapshot={...t,status:"error",error:e}}switch(this._snapshot.status){case"active":for(const e of this.observers)try{e.next?.(t)}catch(t){y(t)}break;case"done":for(const e of this.observers)try{e.next?.(t)}catch(t){y(t)}this._stopProcedure(),this._complete(),this._doneEvent={type:`xstate.done.actor.${this.id}`,output:this._snapshot.output},this._parent&&this.system._relay(this,this._parent,this._doneEvent);break;case"error":this._error(this._snapshot.error)}this.system._sendInspectionEvent({type:"@xstate.snapshot",actorRef:this,event:e,snapshot:t})}subscribe(t,e,s){const n=j(t,e,s);if(this._processingStatus!==N.Stopped)this.observers.add(n);else switch(this._snapshot.status){case"done":try{n.complete?.()}catch(t){y(t)}break;case"error":{const t=this._snapshot.error;if(n.error)try{n.error(t)}catch(t){y(t)}else y(t);break}}return{unsubscribe:()=>{this.observers.delete(n)}}}on(t,e){let s=this.eventListeners.get(t);s||(s=new Set,this.eventListeners.set(t,s));const n=e.bind(void 0);return s.add(n),{unsubscribe:()=>{s.delete(n)}}}start(){if(this._processingStatus===N.Running)return this;this._syncSnapshot&&this.subscribe({next:t=>{"active"===t.status&&this.system._relay(this,this._parent,{type:`xstate.snapshot.${this.id}`,snapshot:t})},error:()=>{}}),this.system._register(this.sessionId,this),this._systemId&&this.system._set(this._systemId,this),this._processingStatus=N.Running;const t=l(this.options.input);switch(this.system._sendInspectionEvent({type:"@xstate.event",sourceRef:this._parent,actorRef:this,event:t}),this._snapshot.status){case"done":return this.update(this._snapshot,t),this;case"error":return this._error(this._snapshot.error),this}if(this._parent||this.system.start(),this.logic.start)try{this.logic.start(this._snapshot,this._actorScope)}catch(t){return this._snapshot={...this._snapshot,status:"error",error:t},this._error(t),this}return this.update(this._snapshot,t),this.options.devTools&&this.attachDevTools(),this.mailbox.start(),this}_process(t){let e,s;try{e=this.logic.transition(this._snapshot,t,this._actorScope)}catch(t){s={err:t}}if(s){const{err:t}=s;return this._snapshot={...this._snapshot,status:"error",error:t},void this._error(t)}this.update(e,t),t.type===f&&(this._stopProcedure(),this._complete())}_stop(){return this._processingStatus===N.Stopped?this:(this.mailbox.clear(),this._processingStatus===N.NotStarted?(this._processingStatus=N.Stopped,this):(this.mailbox.enqueue({type:f}),this))}stop(){if(this._parent)throw new Error("A non-root actor cannot be stopped directly.");return this._stop()}_complete(){for(const t of this.observers)try{t.complete?.()}catch(t){y(t)}this.observers.clear()}_reportError(t){if(!this.observers.size)return void(this._parent||y(t));let e=!1;for(const s of this.observers){const n=s.error;e||=!n;try{n?.(t)}catch(t){y(t)}}this.observers.clear(),e&&y(t)}_error(t){this._stopProcedure(),this._reportError(t),this._parent&&this.system._relay(this,this._parent,p(this.id,t))}_stopProcedure(){return this._processingStatus!==N.Running||(this.system.scheduler.cancelAll(this),this.mailbox.clear(),this.mailbox=new o(this._process.bind(this)),this._processingStatus=N.Stopped,this.system._unregister(this)),this}_send(t){this._processingStatus!==N.Stopped&&this.mailbox.enqueue(t)}send(t){this.system._relay(void 0,this,t)}attachDevTools(){const{devTools:t}=this.options;t&&("function"==typeof t?t:n.s)(this)}toJSON(){return{xstate$$type:M,id:this.id}}getPersistedSnapshot(t){return this.logic.getPersistedSnapshot(this._snapshot,t)}[g](){return this}getSnapshot(){return this._snapshot}}function C(t,...[e]){return new P(t,e)}function z(t,e,s,n,{sendId:o}){return[e,"function"==typeof o?o(s,n):o]}function D(t,e){t.defer((()=>{t.system.scheduler.cancel(t.self,e)}))}function V(t){function e(t,e){}return e.type="xstate.cancel",e.sendId=t,e.resolve=z,e.execute=D,e}function J(t,e,s,n,{id:o,systemId:i,src:r,input:a,syncSnapshot:c}){const u="string"==typeof r?T(e.machine,r):r,h="function"==typeof o?o(s):o;let f;return u&&(f=C(u,{id:h,src:r,parent:t.self,syncSnapshot:c,systemId:i,input:"function"==typeof a?a({context:e.context,event:s.event,self:t.self}):a})),[qt(e,{children:{...e.children,[h]:f}}),{id:o,actorRef:f}]}function B(t,{id:e,actorRef:s}){s&&t.defer((()=>{s._processingStatus!==N.Stopped&&s.start()}))}function W(...[t,{id:e,systemId:s,input:n,syncSnapshot:o=!1}={}]){function i(t,e){}return i.type="snapshot.spawnChild",i.id=e,i.systemId=s,i.src=t,i.input=n,i.syncSnapshot=o,i.resolve=J,i.execute=B,i}function q(t,e,s,n,{actorRef:o}){const i="function"==typeof o?o(s,n):o,r="string"==typeof i?e.children[i]:i;let a=e.children;return r&&(a={...a},delete a[r.id]),[qt(e,{children:a}),r]}function L(t,e){e&&(t.system._unregister(e),e._processingStatus===N.Running?t.defer((()=>{t.stopChild(e)})):t.stopChild(e))}function U(t){function e(t,e){}return e.type="xstate.stopChild",e.actorRef=t,e.resolve=q,e.execute=L,e}function X(t,{context:e,event:s},{guards:n}){return n.some((n=>Q(n,e,s,t)))}function G(t){function e(t,e){return!1}return e.check=X,e.guards=t,e}function Q(t,e,s,n){const{machine:o}=n,i="function"==typeof t,r=i?t:o.implementations.guards["string"==typeof t?t:t.type];if(!i&&!r)throw new Error(`Guard '${"string"==typeof t?t:t.type}' is not implemented.'.`);if("function"!=typeof r)return Q(r,e,s,n);const a={context:e,event:s},c=i||"string"==typeof t?void 0:"params"in t?"function"==typeof t.params?t.params({context:e,event:s}):t.params:void 0;return"check"in r?r.check(n,a,r):r(a,c)}const F=t=>"atomic"===t.type||"final"===t.type;function H(t){return Object.values(t.states).filter((t=>"history"!==t.type))}function K(t,e){const s=[];if(e===t)return s;let n=t.parent;for(;n&&n!==e;)s.push(n),n=n.parent;return s}function Y(t){const e=new Set(t),s=tt(e);for(const t of e)if("compound"!==t.type||s.get(t)&&s.get(t).length){if("parallel"===t.type)for(const s of H(t))if("history"!==s.type&&!e.has(s)){const t=ft(s);for(const s of t)e.add(s)}}else ft(t).forEach((t=>e.add(t)));for(const t of e){let s=t.parent;for(;s;)e.add(s),s=s.parent}return e}function Z(t,e){const s=e.get(t);if(!s)return{};if("compound"===t.type){const t=s[0];if(!t)return{};if(F(t))return t.key}const n={};for(const t of s)n[t.key]=Z(t,e);return n}function tt(t){const e=new Map;for(const s of t)e.has(s)||e.set(s,[]),s.parent&&(e.has(s.parent)||e.set(s.parent,[]),e.get(s.parent).push(s));return e}function et(t,e){return Z(t,tt(Y(e)))}function st(t,e){return"compound"===e.type?H(e).some((e=>"final"===e.type&&t.has(e))):"parallel"===e.type?H(e).every((e=>st(t,e))):"final"===e.type}const nt=t=>"#"===t[0];function ot(t,e){return t.transitions.get(e)||[...t.transitions.keys()].filter((t=>{if(t===c)return!0;if(!t.endsWith(".*"))return!1;const s=t.split("."),n=e.split(".");for(let t=0;t<s.length;t++){const e=s[t],o=n[t];if("*"===e)return t===s.length-1;if(e!==o)return!1}return!0})).sort(((t,e)=>e.length-t.length)).flatMap((e=>t.transitions.get(e)))}function it(t){const e=t.config.after;return e?Object.keys(e).flatMap(((s,n)=>{const o=e[s],i="string"==typeof o?{target:o}:o,r=Number.isNaN(+s)?s:+s,a=(e=>{const s={type:`xstate.after.${e}.${t.id}`};const n=s.type;return t.entry.push(Qt(s,{id:n,delay:e})),t.exit.push(V(n)),n})(r);return k(i).map((t=>({...t,event:a,delay:r})))})).map((e=>{const{delay:s}=e;return{...rt(t,e.event,e),delay:s}})):[]}function rt(t,e,s){const n=O(s.target),o=s.reenter??!1,r=function(t,e){if(void 0!==e)return e.map((e=>{if("string"!=typeof e)return e;if(nt(e))return t.machine.getStateNodeById(e);const s=e[0]===i;if(s&&!t.parent)return lt(t,e.slice(1));const n=s?t.key+e:e;if(!t.parent)throw new Error(`Invalid target: "${e}" is not a valid target from the root node. Did you mean ".${e}"?`);try{return lt(t.parent,n)}catch(e){throw new Error(`Invalid transition definition for state node '${t.id}':\n${e.message}`)}}))}(t,n),a={...s,actions:k(s.actions),guard:s.guard,target:r,source:t,reenter:o,eventType:e,toJSON:()=>({...a,source:`#${t.id}`,target:r?r.map((t=>`#${t.id}`)):void 0})};return a}function at(t){const e=new Map;if(t.config.on)for(const s of Object.keys(t.config.on)){if(s===a)throw new Error('Null events ("") cannot be specified as a transition key. Use `always: { ... }` instead.');const n=t.config.on[s];e.set(s,I(n).map((e=>rt(t,s,e))))}if(t.config.onDone){const s=`xstate.done.state.${t.id}`;e.set(s,I(t.config.onDone).map((e=>rt(t,s,e))))}for(const s of t.invoke){if(s.onDone){const n=`xstate.done.actor.${s.id}`;e.set(n,I(s.onDone).map((e=>rt(t,n,e))))}if(s.onError){const n=`xstate.error.actor.${s.id}`;e.set(n,I(s.onError).map((e=>rt(t,n,e))))}if(s.onSnapshot){const n=`xstate.snapshot.${s.id}`;e.set(n,I(s.onSnapshot).map((e=>rt(t,n,e))))}}for(const s of t.after){let t=e.get(s.eventType);t||(t=[],e.set(s.eventType,t)),t.push(s)}return e}function ct(t,e){const s="string"==typeof e?t.states[e]:e?t.states[e.target]:void 0;if(!s&&e)throw new Error(`Initial state node "${e}" not found on parent state node #${t.id}`);const n={source:t,actions:e&&"string"!=typeof e?k(e.actions):[],eventType:null,reenter:!1,target:s?[s]:[],toJSON:()=>({...n,source:`#${t.id}`,target:s?[`#${s.id}`]:[]})};return n}function ut(t){const e=O(t.config.target);return e?{target:e.map((e=>"string"==typeof e?lt(t.parent,e):e))}:t.parent.initial}function ht(t){return"history"===t.type}function ft(t){const e=dt(t);for(const s of e)for(const n of K(s,t))e.add(n);return e}function dt(t){const e=new Set;return function t(s){if(!e.has(s))if(e.add(s),"compound"===s.type)t(s.initial.target[0]);else if("parallel"===s.type)for(const e of H(s))t(e)}(t),e}function pt(t,e){if(nt(e))return t.machine.getStateNodeById(e);if(!t.states)throw new Error(`Unable to retrieve child state '${e}' from '${t.id}'; no child states exist.`);const s=t.states[e];if(!s)throw new Error(`Child state '${e}' does not exist on '${t.id}'`);return s}function lt(t,e){if("string"==typeof e&&nt(e))try{return t.machine.getStateNodeById(e)}catch(t){}const s=S(e).slice();let n=t;for(;s.length;){const t=s.shift();if(!t.length)break;n=pt(n,t)}return n}function yt(t,e){if("string"==typeof e){const s=t.states[e];if(!s)throw new Error(`State '${e}' does not exist on '${t.id}'`);return[t,s]}const s=Object.keys(e),n=s.map((e=>pt(t,e))).filter(Boolean);return[t.machine.root,t].concat(n,s.reduce(((s,n)=>{const o=pt(t,n);if(!o)return s;const i=yt(o,e[n]);return s.concat(i)}),[]))}function gt(t,e,s,n){return"string"==typeof e?function(t,e,s,n){const o=pt(t,e).next(s,n);return o&&o.length?o:t.next(s,n)}(t,e,s,n):1===Object.keys(e).length?function(t,e,s,n){const o=Object.keys(e),i=gt(pt(t,o[0]),e[o[0]],s,n);return i&&i.length?i:t.next(s,n)}(t,e,s,n):function(t,e,s,n){const o=[];for(const i of Object.keys(e)){const r=e[i];if(!r)continue;const a=gt(pt(t,i),r,s,n);a&&o.push(...a)}return o.length?o:t.next(s,n)}(t,e,s,n)}function vt(t){return Object.keys(t.states).map((e=>t.states[e])).filter((t=>"history"===t.type))}function mt(t,e){let s=t;for(;s.parent&&s.parent!==e;)s=s.parent;return s.parent===e}function _t(t,e){const s=new Set(t),n=new Set(e);for(const t of s)if(n.has(t))return!0;for(const t of n)if(s.has(t))return!0;return!1}function St(t,e,s){const n=new Set;for(const o of t){let t=!1;const i=new Set;for(const r of n)if(_t(xt([o],e,s),xt([r],e,s))){if(!mt(o.source,r.source)){t=!0;break}i.add(r)}if(!t){for(const t of i)n.delete(t);n.add(o)}}return Array.from(n)}function bt(t,e){if(!t.target)return[];const s=new Set;for(const n of t.target)if(ht(n))if(e[n.id])for(const t of e[n.id])s.add(t);else for(const t of bt(ut(n),e))s.add(t);else s.add(n);return[...s]}function wt(t,e){const s=bt(t,e);if(!s)return;if(!t.reenter&&s.every((e=>e===t.source||mt(e,t.source))))return t.source;return function(t){const[e,...s]=t;for(const t of K(e,void 0))if(s.every((e=>mt(e,t))))return t}(s.concat(t.source))||(t.reenter?void 0:t.source.machine.root)}function xt(t,e,s){const n=new Set;for(const o of t)if(o.target?.length){const t=wt(o,s);o.reenter&&o.source===t&&n.add(t);for(const s of e)mt(s,t)&&n.add(s)}return[...n]}function kt(t,e,s,n,o,i){if(!t.length)return e;const r=new Set(e._nodes);let a=e.historyValue;const c=St(t,r,a);let u=e;o||([u,a]=function(t,e,s,n,o,i,r){let a=t;const c=xt(n,o,i);let u;c.sort(((t,e)=>e.order-t.order));for(const t of c)for(const e of vt(t)){let s;s="deep"===e.history?e=>F(e)&&mt(e,t):e=>e.parent===t,u??={...i},u[e.id]=Array.from(o).filter(s)}for(const t of c)a=Tt(a,e,s,[...t.exit,...t.invoke.map((t=>U(t.id)))],r),o.delete(t);return[a,u||i]}(u,n,s,c,r,a,i)),u=Tt(u,n,s,c.flatMap((t=>t.actions)),i),u=function(t,e,s,n,o,i,r,a){let c=t;const u=new Set,h=new Set;(function(t,e,s,n){for(const o of t){const t=wt(o,e);for(const i of o.target||[])ht(i)||o.source===i&&o.source===t&&!o.reenter||(n.add(i),s.add(i)),$t(i,e,s,n);const i=bt(o,e);for(const r of i){const i=K(r,t);"parallel"===t?.type&&i.push(t),It(n,e,s,i,!o.source.parent&&o.reenter?void 0:t)}}})(n,r,h,u),a&&h.add(t.machine.root);const f=new Set;for(const t of[...u].sort(((t,e)=>t.order-e.order))){o.add(t);const n=[];n.push(...t.entry);for(const e of t.invoke)n.push(W(e.src,{...e,syncSnapshot:!!e.onSnapshot}));if(h.has(t)){const e=t.initial.actions;n.push(...e)}if(c=Tt(c,e,s,n,i,t.invoke.map((t=>t.id))),"final"===t.type){const n=t.parent;let r="parallel"===n?.type?n:n?.parent,a=r||t;for("compound"===n?.type&&i.push(d(n.id,void 0!==t.output?E(t.output,c.context,e,s.self):void 0));"parallel"===r?.type&&!f.has(r)&&st(o,r);)f.add(r),i.push(d(r.id)),a=r,r=r.parent;if(r)continue;c=qt(c,{status:"done",output:Et(c,e,s,c.machine.root,a)})}}return c}(u,n,s,c,r,i,a,o);const h=[...r];"done"===u.status&&(u=Tt(u,n,s,h.sort(((t,e)=>e.order-t.order)).flatMap((t=>t.exit)),i));try{return a===e.historyValue&&function(t,e){if(t.length!==e.size)return!1;for(const s of t)if(!e.has(s))return!1;return!0}(e._nodes,r)?u:qt(u,{_nodes:h,historyValue:a})}catch(t){throw t}}function Et(t,e,s,n,o){if(void 0===n.output)return;const i=d(o.id,void 0!==o.output&&o.parent?E(o.output,t.context,e,s.self):void 0);return E(n.output,t.context,i,s.self)}function $t(t,e,s,n){if(ht(t))if(e[t.id]){const o=e[t.id];for(const t of o)n.add(t),$t(t,e,s,n);for(const i of o)Ot(i,t.parent,n,e,s)}else{const o=ut(t);for(const i of o.target)n.add(i),o===t.parent?.initial&&s.add(t.parent),$t(i,e,s,n);for(const i of o.target)Ot(i,t.parent,n,e,s)}else if("compound"===t.type){const[o]=t.initial.target;ht(o)||(n.add(o),s.add(o)),$t(o,e,s,n),Ot(o,t,n,e,s)}else if("parallel"===t.type)for(const o of H(t).filter((t=>!ht(t))))[...n].some((t=>mt(t,o)))||(ht(o)||(n.add(o),s.add(o)),$t(o,e,s,n))}function It(t,e,s,n,o){for(const i of n)if(o&&!mt(i,o)||t.add(i),"parallel"===i.type)for(const n of H(i).filter((t=>!ht(t))))[...t].some((t=>mt(t,n)))||(t.add(n),$t(n,e,s,t))}function Ot(t,e,s,n,o){It(s,n,o,K(t,e))}let jt=!1;function At(t,e,s,n,o,i){const{machine:r}=t;let a=t;for(const c of n){const u="function"==typeof c,h=u?c:r.implementations.actions["string"==typeof c?c:c.type];if(!h)continue;const f={context:a.context,event:e,self:s.self,system:s.system},d=u||"string"==typeof c?void 0:"params"in c?"function"==typeof c.params?c.params({context:a.context,event:e}):c.params:void 0;function p(){s.system._sendInspectionEvent({type:"@xstate.action",actorRef:s.self,action:{type:"string"==typeof c?c:"object"==typeof c?c.type:c.name||"(anonymous)",params:d}});try{jt=h,h(f,d)}finally{jt=!1}}if(!("resolve"in h)){s.self._processingStatus===N.Running?p():s.defer((()=>{p()}));continue}const l=h,[y,g,v]=l.resolve(s,a,f,d,h,o);a=y,"retryResolve"in l&&i?.push([l,g]),"execute"in l&&(s.self._processingStatus===N.Running?l.execute(s,g):s.defer(l.execute.bind(null,s,g))),v&&(a=At(a,e,s,v,o,i))}return a}function Tt(t,e,s,n,o,i){const r=i?[]:void 0,a=At(t,e,s,n,{internalQueue:o,deferredActorIds:i},r);return r?.forEach((([t,e])=>{t.retryResolve(s,a,e)})),a}function Mt(t,e,s,n=[]){let o=t;const i=[];function r(t,e,n){s.system._sendInspectionEvent({type:"@xstate.microstep",actorRef:s.self,event:e,snapshot:t,_transitions:n}),i.push(t)}if(e.type===f)return o=qt(Nt(o,e,s),{status:"stopped"}),r(o,e,[]),{snapshot:o,microstates:i};let a=e;if(a.type!==u){const e=a,c=function(t){return t.type.startsWith("xstate.error.actor")}(e),u=Rt(e,o);if(c&&!u.length)return o=qt(t,{status:"error",error:e.error}),r(o,e,[]),{snapshot:o,microstates:i};o=kt(u,t,s,a,!1,n),r(o,e,u)}let c=!0;for(;"active"===o.status;){let t=c?Pt(o,a):[];const e=t.length?o:void 0;if(!t.length){if(!n.length)break;a=n.shift(),t=Rt(a,o)}o=kt(t,o,s,a,!1,n),c=o!==e,r(o,a,t)}return"active"!==o.status&&Nt(o,a,s),{snapshot:o,microstates:i}}function Nt(t,e,s){return Tt(t,e,s,Object.values(t.children).map((t=>U(t))),[])}function Rt(t,e){return e.machine.getTransitionData(e,t)}function Pt(t,e){const s=new Set,n=t._nodes.filter(F);for(const o of n)t:for(const n of[o].concat(K(o,void 0)))if(n.always)for(const o of n.always)if(void 0===o.guard||Q(o.guard,t.context,e,t)){s.add(o);break t}return St(Array.from(s),new Set(t._nodes),t.historyValue)}function Ct(t,e){return et(t,[...Y(yt(t,e))])}const zt=function(t){return _(t,this.value)},Dt=function(t){return this.tags.has(t)},Vt=function(t){const e=this.machine.getTransitionData(this,t);return!!e?.length&&e.some((t=>void 0!==t.target||t.actions.length))},Jt=function(){const{_nodes:t,tags:e,machine:s,getMeta:n,toJSON:o,can:i,hasTag:r,matches:a,...c}=this;return{...c,tags:Array.from(e)}},Bt=function(){return this._nodes.reduce(((t,e)=>(void 0!==e.meta&&(t[e.id]=e.meta),t)),{})};function Wt(t,e){return{status:t.status,output:t.output,error:t.error,machine:e,context:t.context,_nodes:t._nodes,value:et(e.root,t._nodes),tags:new Set(t._nodes.flatMap((t=>t.tags))),children:t.children,historyValue:t.historyValue||{},matches:zt,hasTag:Dt,can:Vt,getMeta:Bt,toJSON:Jt}}function qt(t,e={}){return Wt({...t,...e},t.machine)}function Lt(t,e){const{_nodes:s,tags:n,machine:o,children:i,context:r,can:a,hasTag:c,matches:u,getMeta:h,toJSON:f,...d}=t,p={};for(const t in i){const s=i[t];p[t]={snapshot:s.getPersistedSnapshot(e),src:s.src,systemId:s._systemId,syncSnapshot:s._syncSnapshot}}return{...d,context:Ut(r),children:p}}function Ut(t){let e;for(const s in t){const n=t[s];if(n&&"object"==typeof n)if("sessionId"in n&&"send"in n&&"ref"in n)e??=Array.isArray(t)?t.slice():{...t},e[s]={xstate$$type:M,id:n.id};else{const o=Ut(n);o!==n&&(e??=Array.isArray(t)?t.slice():{...t},e[s]=o)}}return e??t}function Xt(t,e,s,n,{event:o,id:i,delay:r},{internalQueue:a}){const c=e.machine.implementations.delays;if("string"==typeof o)throw new Error(`Only event objects may be used with raise; use raise({ type: "${o}" }) instead`);const u="function"==typeof o?o(s,n):o;let h;if("string"==typeof r){const t=c&&c[r];h="function"==typeof t?t(s,n):t}else h="function"==typeof r?r(s,n):r;return"number"!=typeof h&&a.push(u),[e,{event:u,id:i,delay:h}]}function Gt(t,e){const{event:s,delay:n,id:o}=e;"number"!=typeof n||t.defer((()=>{const e=t.self;t.system.scheduler.schedule(e,e,s,n,o)}))}function Qt(t,e){function s(t,e){}return s.type="xstate.raise",s.event=t,s.id=e?.id,s.delay=e?.delay,s.resolve=Xt,s.execute=Gt,s}},97233:(t,e,s)=>{"use strict";s.d(e,{DT:()=>p,Op:()=>f,mj:()=>d}),s(53187);var n=s(4316),o=s(71529);const i=new WeakMap;function r(t,e,s){let n=i.get(t);return n?e in n||(n[e]=s()):(n={[e]:s()},i.set(t,n)),n[e]}const a={},c=t=>"string"==typeof t?{type:t}:"function"==typeof t?"resolve"in t?{type:t.type}:{type:t.name}:t;class u{constructor(t,e){if(this.config=t,this.key=void 0,this.id=void 0,this.type=void 0,this.path=void 0,this.states=void 0,this.history=void 0,this.entry=void 0,this.exit=void 0,this.parent=void 0,this.machine=void 0,this.meta=void 0,this.output=void 0,this.order=-1,this.description=void 0,this.tags=[],this.transitions=void 0,this.always=void 0,this.parent=e._parent,this.key=e._key,this.machine=e._machine,this.path=this.parent?this.parent.path.concat(this.key):[],this.id=this.config.id||[this.machine.id,...this.path].join(n.S),this.type=this.config.type||(this.config.states&&Object.keys(this.config.states).length?"compound":this.config.history?"history":"atomic"),this.description=this.config.description,this.order=this.machine.idMap.size,this.machine.idMap.set(this.id,this),this.states=this.config.states?(0,n.m)(this.config.states,((t,e)=>new u(t,{_parent:this,_key:e,_machine:this.machine}))):a,"compound"===this.type&&!this.config.initial)throw new Error(`No initial state specified for compound state node "#${this.id}". Try adding { initial: "${Object.keys(this.states)[0]}" } to the state config.`);this.history=!0===this.config.history?"shallow":this.config.history||!1,this.entry=(0,n.t)(this.config.entry).slice(),this.exit=(0,n.t)(this.config.exit).slice(),this.meta=this.config.meta,this.output="final"!==this.type&&this.parent?void 0:this.config.output,this.tags=(0,n.t)(t.tags).slice()}_initialize(){this.transitions=(0,n.f)(this),this.config.always&&(this.always=(0,n.a)(this.config.always).map((t=>(0,n.b)(this,n.N,t)))),Object.keys(this.states).forEach((t=>{this.states[t]._initialize()}))}get definition(){return{id:this.id,key:this.key,version:this.machine.version,type:this.type,initial:this.initial?{target:this.initial.target,source:this,actions:this.initial.actions.map(c),eventType:null,reenter:!1,toJSON:()=>({target:this.initial.target.map((t=>`#${t.id}`)),source:`#${this.id}`,actions:this.initial.actions.map(c),eventType:null})}:void 0,history:this.history,states:(0,n.m)(this.states,(t=>t.definition)),on:this.on,transitions:[...this.transitions.values()].flat().map((t=>({...t,actions:t.actions.map(c)}))),entry:this.entry.map(c),exit:this.exit.map(c),meta:this.meta,order:this.order||-1,output:this.output,invoke:this.invoke,description:this.description,tags:this.tags}}toJSON(){return this.definition}get invoke(){return r(this,"invoke",(()=>(0,n.t)(this.config.invoke).map(((t,e)=>{const{src:s,systemId:o}=t,i=t.id??(0,n.c)(this.id,e),r="string"==typeof s?s:`xstate.invoke.${(0,n.c)(this.id,e)}`;return{...t,src:r,id:i,systemId:o,toJSON(){const{onDone:e,onError:s,...n}=t;return{...n,type:"xstate.invoke",src:r,id:i}}}}))))}get on(){return r(this,"on",(()=>[...this.transitions].flatMap((([t,e])=>e.map((e=>[t,e])))).reduce(((t,[e,s])=>(t[e]=t[e]||[],t[e].push(s),t)),{})))}get after(){return r(this,"delayedTransitions",(()=>(0,n.g)(this)))}get initial(){return r(this,"initial",(()=>(0,n.d)(this,this.config.initial)))}next(t,e){const s=e.type,o=[];let i;const a=r(this,`candidates-${s}`,(()=>(0,n.h)(this,s)));for(const r of a){const{guard:a}=r,c=t.context;let u=!1;try{u=!a||(0,n.e)(a,c,e,t)}catch(t){const e="string"==typeof a?a:"object"==typeof a?a.type:void 0;throw new Error(`Unable to evaluate guard ${e?`'${e}' `:""}in transition for event '${s}' in state node '${this.id}':\n${t.message}`)}if(u){o.push(...r.actions),i=r;break}}return i?[i]:void 0}get events(){return r(this,"events",(()=>{const{states:t}=this,e=new Set(this.ownEvents);if(t)for(const s of Object.keys(t)){const n=t[s];if(n.states)for(const t of n.events)e.add(`${t}`)}return Array.from(e)}))}get ownEvents(){const t=new Set([...this.transitions.keys()].filter((t=>this.transitions.get(t).some((t=>!(!t.target&&!t.actions.length&&!t.reenter))))));return Array.from(t)}}class h{constructor(t,e){this.config=t,this.version=void 0,this.schemas=void 0,this.implementations=void 0,this.__xstatenode=!0,this.idMap=new Map,this.root=void 0,this.id=void 0,this.states=void 0,this.events=void 0,this.__TResolvedTypesMeta=void 0,this.id=t.id||"(machine)",this.implementations={actors:e?.actors??{},actions:e?.actions??{},delays:e?.delays??{},guards:e?.guards??{}},this.version=this.config.version,this.schemas=this.config.schemas,this.transition=this.transition.bind(this),this.getInitialSnapshot=this.getInitialSnapshot.bind(this),this.getPersistedSnapshot=this.getPersistedSnapshot.bind(this),this.restoreSnapshot=this.restoreSnapshot.bind(this),this.start=this.start.bind(this),this.root=new u(t,{_key:this.id,_machine:this}),this.root._initialize(),this.states=this.root.states,this.events=this.root.events}provide(t){const{actions:e,guards:s,actors:n,delays:o}=this.implementations;return new h(this.config,{actions:{...e,...t.actions},guards:{...s,...t.guards},actors:{...n,...t.actors},delays:{...o,...t.delays}})}resolveState(t){const e=(0,n.r)(this.root,t.value),s=(0,n.i)((0,n.j)(this.root,e));return(0,n.k)({_nodes:[...s],context:t.context||{},children:{},status:(0,n.l)(s,this.root)?"done":t.status||"active",output:t.output,error:t.error,historyValue:t.historyValue},this)}transition(t,e,s){return(0,n.n)(t,e,s).snapshot}microstep(t,e,s){return(0,n.n)(t,e,s).microstates}getTransitionData(t,e){return(0,n.o)(this.root,t.value,t,e)||[]}getPreInitialState(t,e,s){const{context:i}=this.config,r=(0,n.k)({context:"function"!=typeof i&&i?i:{},_nodes:[this.root],children:{},status:"active"},this);if("function"==typeof i){const a=({spawn:t,event:e,self:s})=>i({spawn:t,input:e.input,self:s});return(0,n.p)(r,e,t,[(0,o.a)(a)],s)}return r}getInitialSnapshot(t,e){const s=(0,n.q)(e),o=[],i=this.getPreInitialState(t,s,o),r=(0,n.s)([{target:[...(0,n.u)(this.root)],source:this.root,reenter:!0,actions:[],eventType:null,toJSON:null}],i,t,s,!0,o),{snapshot:a}=(0,n.n)(r,s,t,o);return a}start(t){Object.values(t.children).forEach((t=>{"active"===t.getSnapshot().status&&t.start()}))}getStateNodeById(t){const e=(0,n.v)(t),s=e.slice(1),o=(0,n.w)(e[0])?e[0].slice(1):e[0],i=this.idMap.get(o);if(!i)throw new Error(`Child state node '#${o}' does not exist on machine '${this.id}'`);return(0,n.x)(i,s)}get definition(){return this.root.definition}toJSON(){return this.definition}getPersistedSnapshot(t,e){return(0,n.y)(t,e)}restoreSnapshot(t,e){const s={},o=t.children;Object.keys(o).forEach((t=>{const i=o[t],r=i.snapshot,a=i.src,c="string"==typeof a?(0,n.z)(this,a):a;if(!c)return;const u=(0,n.A)(c,{id:t,parent:e.self,syncSnapshot:i.syncSnapshot,snapshot:r,src:a,systemId:i.systemId});s[t]=u}));const i=(0,n.k)({...t,children:s,_nodes:Array.from((0,n.i)((0,n.j)(this.root,t.value)))},this);let r=new Set;return function t(e,s){if(!r.has(e)){r.add(e);for(let o in e){const i=e[o];if(i&&"object"==typeof i){if("xstate$$type"in i&&i.xstate$$type===n.$){e[o]=s[i.id];continue}t(i,s)}}}}(i.context,s),i}}function f(t,e){return new h(t,e)}function d({schemas:t,actors:e,actions:s,guards:n,delays:o}){return{createMachine:i=>f({...i,schemas:t},{actors:e,actions:s,guards:n,delays:o})}}function p(t,e){const s=(0,n.t)(e);if(!s.includes(t.type)){const e=1===s.length?`type "${s[0]}"`:`one of types "${s.join('", "')}"`;throw new Error(`Expected event ${JSON.stringify(t)} to have ${e}`)}}}}]);
/*! For license information please see activity-panels-setup.js.LICENSE.txt */
(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[2464],{72744:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var o=n(5573),r=n(39793);const a=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.<PERSON>,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})})},46445:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var o=n(5573),r=n(39793);const a=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})})},97897:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var o=n(5573),r=n(39793);const a=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.Path,{d:"M12 3.3c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8s-4-8.8-8.8-8.8zm6.5 5.5h-2.6C15.4 7.3 14.8 6 14 5c2 .6 3.6 2 4.5 3.8zm.7 3.2c0 .6-.1 1.2-.2 1.8h-2.9c.1-.6.1-1.2.1-1.8s-.1-1.2-.1-1.8H19c.2.6.2 1.2.2 1.8zM12 18.7c-1-.7-1.8-1.9-2.3-3.5h4.6c-.5 1.6-1.3 2.9-2.3 3.5zm-2.6-4.9c-.1-.6-.1-1.1-.1-1.8 0-.6.1-1.2.1-1.8h5.2c.1.6.1 1.1.1 1.8s-.1 1.2-.1 1.8H9.4zM4.8 12c0-.6.1-1.2.2-1.8h2.9c-.1.6-.1 1.2-.1 1.8 0 .6.1 1.2.1 1.8H5c-.2-.6-.2-1.2-.2-1.8zM12 5.3c1 .7 1.8 1.9 2.3 3.5H9.7c.5-1.6 1.3-2.9 2.3-3.5zM10 5c-.8 1-1.4 2.3-1.8 3.8H5.5C6.4 7 8 5.6 10 5zM5.5 15.3h2.6c.4 1.5 1 2.8 1.8 3.7-1.8-.6-3.5-2-4.4-3.7zM14 19c.8-1 1.4-2.2 1.8-3.7h2.6C17.6 17 16 18.4 14 19z"})})},37148:(e,t,n)=>{"use strict";n.d(t,{a:()=>c});var o=n(17697),r=n.n(o),a=n(86087),s=n(23772);const c=({children:e,className:t})=>(0,a.createElement)("div",{className:r()("woocommerce-onboarding-loader",t)},e);c.Layout=({children:e,className:t})=>(0,a.createElement)("div",{className:r()("woocommerce-onboarding-loader-wrapper",t)},(0,a.createElement)("div",{className:r()("woocommerce-onboarding-loader-container",t)},e)),c.Illustration=({children:e})=>(0,a.createElement)(a.Fragment,null,e),c.Title=({children:e,className:t})=>(0,a.createElement)("h1",{className:r()("woocommerce-onboarding-loader__title",t)},e),c.ProgressBar=({progress:e,className:t})=>(0,a.createElement)(s.A,{className:r()("progress-bar",t),percent:null!=e?e:0,color:"var(--wp-admin-theme-color)",bgcolor:"#E0E0E0"}),c.Subtext=({children:e,className:t})=>(0,a.createElement)("p",{className:r()("woocommerce-onboarding-loader__paragraph",t)},e),c.Sequence=({interval:e,shouldLoop:t=!0,children:n,onChange:o=()=>{}})=>{const[r,s]=(0,a.useState)(0),c=a.Children.count(n);(0,a.useEffect)((()=>{const n=setInterval((()=>{s((e=>{const r=e+1;if(t){const e=r%c;return o(e),e}return r<c?(o(r),r):(clearInterval(n),e)}))}),e);return()=>clearInterval(n)}),[e,n,t,c]);const l=a.Children.toArray(n)[r];return(0,a.createElement)(a.Fragment,null,l)}},14695:(e,t,n)=>{"use strict";n.d(t,{L:()=>S});var o=n(51609),r=n(86087),a=n(56427),s=n(18015),c=n(12486),l=n(27193),i=n(6457),m=n(94084),d=n(90423),p=n(31183),u=n(76147),h=n(22056),g=n(96043),w=n(70145),y=n(61727),f=n(97021),v=n(27707),k=n(30487),b=n(97723),_=n(80019),x=n(66012),E=n(17479),j=n(93342),A=n(10630);const N=[{name:"visa",component:(0,r.createElement)(s.A,{key:"visa"})},{name:"mastercard",component:(0,r.createElement)(c.A,{key:"mastercard"})},{name:"amex",component:(0,r.createElement)(l.A,{key:"amex"})},{name:"discover",component:(0,r.createElement)(i.A,{key:"discover"})},{name:"woopay",component:(0,r.createElement)(u.A,{key:"woopay"})},{name:"applepay",component:(0,r.createElement)(m.A,{key:"applepay"})},{name:"googlepay",component:(0,r.createElement)(d.A,{key:"googlepay"})},{name:"afterpay",component:(0,r.createElement)(h.A,{key:"afterpay"})},{name:"affirm",component:(0,r.createElement)(g.A,{key:"affirm"})},{name:"klarna",component:(0,r.createElement)(w.A,{key:"klarna"})},{name:"cartebancaire",component:(0,r.createElement)(y.A,{key:"cartebancaire"})},{name:"unionpay",component:(0,r.createElement)(f.A,{key:"unionpay"})},{name:"diners",component:(0,r.createElement)(v.A,{key:"diners"})},{name:"eftpos",component:(0,r.createElement)(k.A,{key:"eftpos"})},{name:"jcb",component:(0,r.createElement)(p.A,{key:"jcb"})},{name:"bancontact",component:(0,r.createElement)(_.A,{key:"bancontact"})},{name:"becs",component:(0,r.createElement)(E.A,{key:"becs"})},{name:"eps",component:(0,r.createElement)(x.A,{key:"eps"})},{name:"ideal",component:(0,r.createElement)(b.A,{key:"ideal"})},{name:"przelewy24",component:(0,r.createElement)(j.A,{key:"przelewy24"})},{name:"grabpay",component:(0,r.createElement)(A.A,{key:"grabpay"})}],S=({isWooPayEligible:e=!1,maxElements:t=10,tabletWidthBreakpoint:n=768,maxElementsTablet:s=7,mobileWidthBreakpoint:c=480,maxElementsMobile:l=5,totalPaymentMethods:i=21})=>{const[m,d]=(0,o.useState)(t),[p,u]=(0,o.useState)(!1),h=(0,o.useRef)(null),g=e=>{const t=e.target.closest(".woocommerce-woopayments-payment-methods-logos-count");h.current&&t!==h.current||u((e=>!e))},w=e?i:i-1,y=t=>e?t:t+1;(0,o.useEffect)((()=>{const e=()=>{window.innerWidth<=c?d(l):window.innerWidth<=n?d(s):d(t)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[t,l,s,n,c]);const f=N.slice(0,y(m)).filter((t=>e||"woopay"!==t.name)),v=N.slice(y(m)).filter((t=>e||"woopay"!==t.name));return(0,r.createElement)("div",{className:"woocommerce-woopayments-payment-methods-logos"},f.map((e=>e.component)),m<w&&(0,r.createElement)("div",{className:"woocommerce-woopayments-payment-methods-logos-count",role:"button",tabIndex:0,ref:h,onClick:g,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||g(e)}},"+ ",w-m,p&&(0,r.createElement)(a.Popover,{className:"woocommerce-woopayments-payment-methods-logos-popover",placement:"top-start",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:()=>{u(!1)}},(0,r.createElement)("div",{className:"woocommerce-woopayments-payment-methods-logos"},v.map((e=>e.component))))))}},29568:(e,t,n)=>{"use strict";n.d(t,{b:()=>c,J:()=>l});var o=n(84343),r=n.n(o),a=n(27723);const s={PH:{"National Capital Region":(0,a.__)("Metro Manila","woocommerce")},IT:{Rome:(0,a.__)("Roma","woocommerce")}},c=(e,t,n=.7)=>{if(!t)return null;let o=null,a=n;const c=(({country_short:e,region:t="",city:n=""})=>{if(!e)return null;const o=s[e];if(!o)return null;const r=o[t];return r||(o[n]||null)})(t);for(const n of e){if(n.key===t.country_short)return n;if(n.key.split(":")[0]===t.country_short&&n.label.includes("—")){const e=n.label.split("—")[1].trim();if(c===e)return n;if(0===e.localeCompare(t.region||"","en",{sensitivity:"base"})||0===e.localeCompare(t.city||"","en",{sensitivity:"base"}))return n;const s=Math.max(r().compareTwoStrings(e,t.region||""),r().compareTwoStrings(e,t.city||""));s>=a&&(o=n,a=s)}}return o},l=e=>{var t;return null!==(t=e?.split(":")[0])&&void 0!==t?t:void 0}},16081:(e,t,n)=>{"use strict";n.r(t),n.d(t,{SetupTasksPanel:()=>a,default:()=>s});var o=n(87979),r=n(39793);const a=({query:e})=>(0,r.jsx)("div",{className:"woocommerce-setup-panel",children:(0,r.jsx)(o.TaskLists,{query:e})}),s=a},3246:(e,t,n)=>{"use strict";n.d(t,{d:()=>i});var o=n(14908),r=n(36849),a=n(98846),s=n(83306),c=n(27723),l=n(39793);const i=({textProps:e,message:t,eventName:n="",eventProperties:i={},targetUrl:m,linkType:d="wc-admin",target:p,onClickCallback:u})=>{const h=t.match(/{{Link}}(.*?){{\/Link}}/),g=h?h[1]:"",w="external"===d&&"_blank"===p;return(0,l.jsx)(o.Text,{...e,children:(0,r.A)({mixedString:t,components:{Link:(0,l.jsx)(a.Link,{onClick:()=>{if(u?u():(0,s.recordEvent)(n,i),"external"!==d)return window.location.href=m,!1},href:m,type:d,target:w?"_blank":void 0,"aria-label":w?`${g} (${(0,c.__)("opens in a new tab","woocommerce")})`:void 0})}})})}},12974:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>s});var o=n(13240);const r=["a","b","em","i","strong","p","br"],a=["target","href","rel","name","download"],s=e=>({__html:(0,o.sanitize)(e,{ALLOWED_TAGS:r,ALLOWED_ATTR:a})})},13832:(e,t,n)=>{"use strict";n.d(t,{W:()=>r});var o=n(39793);const r=({numTasks:e=5,query:t})=>Boolean(t.task)?null:(0,o.jsx)("div",{className:"woocommerce-task-dashboard__container",children:(0,o.jsxs)("div",{className:"woocommerce-card woocommerce-task-card is-loading","aria-hidden":!0,children:[(0,o.jsx)("div",{className:"woocommerce-card__header",children:(0,o.jsx)("div",{className:"woocommerce-card__title-wrapper",children:(0,o.jsx)("div",{className:"woocommerce-card__title woocommerce-card__header-item",children:(0,o.jsx)("span",{className:"is-placeholder"})})})}),(0,o.jsx)("div",{className:"woocommerce-card__body",children:(0,o.jsx)("div",{className:"woocommerce-list",children:Array.from(new Array(e)).map(((e,t)=>(0,o.jsx)("div",{className:"woocommerce-list__item has-action",children:(0,o.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,o.jsx)("div",{className:"woocommerce-list__item-before",children:(0,o.jsx)("span",{className:"is-placeholder"})}),(0,o.jsx)("div",{className:"woocommerce-list__item-text",children:(0,o.jsx)("div",{className:"woocommerce-list__item-title",children:(0,o.jsx)("span",{className:"is-placeholder"})})}),(0,o.jsx)("div",{className:"woocommerce-list__item-after",children:(0,o.jsx)("span",{className:"is-placeholder"})})]})},t)))})})]})})},72685:(e,t,n)=>{"use strict";n.d(t,{i:()=>w});var o=n(14908),r=n(56427),a=n(98846),s=n(39793);const c="woocommerce_tasklist_experimental_progress_title_item",l=({children:e,order:t=1})=>(0,s.jsx)(r.Fill,{name:c,children:n=>(0,a.createOrderedChildren)(e,t,n)});l.Slot=({fillProps:e})=>(0,s.jsx)(r.Slot,{name:c,fillProps:e,children:a.sortFillsByOrder});var i=n(27723),m=n(86087),d=n(47143),p=n(40314),u=n(15703),h=n(12974);const g=({taskListId:e})=>{const{loading:t,tasksCount:n,completedCount:o,hasVisitedTasks:r}=(0,d.useSelect)((t=>{const n=t(p.onboardingStore).getTaskList(e),o=t(p.onboardingStore).hasFinishedResolution("getTaskList",[e]),r=(0,p.getVisibleTasks)(n?.tasks||[]);return{loading:!o,tasksCount:r?.length,completedCount:r?.filter((e=>e.isComplete)).length,hasVisitedTasks:r?.filter((e=>e.isVisited&&"store_details"!==e.id)).length>0}}),[e]),a=(0,m.useMemo)((()=>{if(!r||o===n){const e=(0,u.getSetting)("siteTitle");return e?(0,i.sprintf)((0,i.__)("Welcome to %s","woocommerce"),e):(0,i.__)("Welcome to your store","woocommerce")}return o<=3?(0,i.__)("Let’s get you started","woocommerce")+"   🚀":o>3&&o<6?(0,i.__)("You’re on the right track","woocommerce"):(0,i.__)("You’re almost there","woocommerce")}),[o,r,n]);return t?null:(0,s.jsx)("h1",{className:"woocommerce-task-progress-header__title",dangerouslySetInnerHTML:(0,h.Ay)(a)})},w=({taskListId:e})=>{const t=(0,o.useSlot)(c);return Boolean(t?.fills?.length)?(0,s.jsx)(l.Slot,{fillProps:{taskListId:e}}):(0,s.jsx)(g,{taskListId:e})}},17697:(e,t)=>{var n;!function(){"use strict";var o={}.hasOwnProperty;function r(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var a=typeof n;if("string"===a||"number"===a)e.push(n);else if(Array.isArray(n)){if(n.length){var s=r.apply(null,n);s&&e.push(s)}}else if("object"===a){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var c in n)o.call(n,c)&&n[c]&&e.push(c)}}}return e.join(" ")}e.exports?(r.default=r,e.exports=r):void 0===(n=function(){return r}.apply(t,[]))||(e.exports=n)}()},94736:(e,t,n)=>{"use strict";t.A=function(e){var t=e.size,n=void 0===t?24:t,o=e.onClick,c=(e.icon,e.className),l=function(e,t){if(null==e)return{};var n,o,r=function(e,t){if(null==e)return{};var n,o,r={},a=Object.keys(e);for(o=0;o<a.length;o++)n=a[o],0<=t.indexOf(n)||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}(e,a),i=["gridicon","gridicons-notice-outline",c,!!function(e){return 0==e%18}(n)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return r.default.createElement("svg",s({className:i,height:n,width:n,onClick:o},l,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),r.default.createElement("g",null,r.default.createElement("path",{d:"M12 4c4.411 0 8 3.589 8 8s-3.589 8-8 8-8-3.589-8-8 3.589-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-2-2h2l.5-6h-3l.5 6z"})))};var o,r=(o=n(51609))&&o.__esModule?o:{default:o},a=["size","onClick","icon","className"];function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t,n=1;n<arguments.length;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},s.apply(this,arguments)}},84343:e=>{function t(e,t){if((e=e.replace(/\s+/g,""))===(t=t.replace(/\s+/g,"")))return 1;if(e.length<2||t.length<2)return 0;let n=new Map;for(let t=0;t<e.length-1;t++){const o=e.substring(t,t+2),r=n.has(o)?n.get(o)+1:1;n.set(o,r)}let o=0;for(let e=0;e<t.length-1;e++){const r=t.substring(e,e+2),a=n.has(r)?n.get(r):0;a>0&&(n.set(r,a-1),o++)}return 2*o/(e.length+t.length-2)}e.exports={compareTwoStrings:t,findBestMatch:function(e,n){if(!function(e,t){return"string"==typeof e&&!!Array.isArray(t)&&!!t.length&&!t.find((function(e){return"string"!=typeof e}))}(e,n))throw new Error("Bad arguments: First argument should be a string, second should be an array of strings");const o=[];let r=0;for(let a=0;a<n.length;a++){const s=n[a],c=t(e,s);o.push({target:s,rating:c}),c>o[r].rating&&(r=a)}return{ratings:o,bestMatch:o[r],bestMatchIndex:r}}}}}]);
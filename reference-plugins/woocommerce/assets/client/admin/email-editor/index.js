(()=>{var e,t={492:(e,t,o)=>{"use strict";var n={};o.r(n),o.d(n,{closeModal:()=>Ee,disableComplementaryArea:()=>ve,enableComplementaryArea:()=>fe,openModal:()=>Te,pinItem:()=>be,setDefaultComplementaryArea:()=>we,setFeatureDefaults:()=>Ce,setFeatureValue:()=>ke,toggleFeature:()=>Se,unpinItem:()=>je});var s={};o.r(s),o.d(s,{getActiveComplementaryArea:()=>Ne,isComplementaryAreaLoading:()=>Pe,isFeatureActive:()=>Me,isItemPinned:()=>Ie,isModalActive:()=>Be});var r={};o.r(r),o.d(r,{changePreviewDeviceType:()=>Sn,closeSidebar:()=>Nn,openSidebar:()=>En,requestSendingNewsletterPreview:()=>Bn,revertAndSaveTemplate:()=>An,saveEditedEmail:()=>In,setIsFetchingPersonalizationTags:()=>Fn,setPersonalizationTagsList:()=>Rn,setTemplateToPost:()=>Mn,toggleFeature:()=>jn,togglePersonalizationTagsModal:()=>Cn,togglePreviewModal:()=>kn,toggleSettingsSidebarActiveTab:()=>Pn,updateSendPreviewEmail:()=>Tn});var i={};o.r(i),o.d(i,{canUserEditGlobalEmailStyles:()=>ss,canUserEditTemplates:()=>Qn,getAutosaveInterval:()=>gs,getBlockPatternsForEmailTemplate:()=>Kn,getCurrentTemplate:()=>os,getCurrentTemplateContent:()=>ns,getDeviceType:()=>_s,getEditedEmailContent:()=>Jn,getEditedPostTemplate:()=>ts,getEmailPostId:()=>as,getEmailTemplates:()=>is,getGlobalEmailStylesPost:()=>rs,getGlobalStylesPostId:()=>ys,getInitialEditorSettings:()=>cs,getPaletteColors:()=>ds,getPersonalizationTagsList:()=>us,getPersonalizationTagsState:()=>ps,getPreviewState:()=>ms,getSentEmailEditorPosts:()=>Xn,getSettingsSidebarActiveTab:()=>ls,getStyles:()=>hs,getTheme:()=>xs,getUrls:()=>ws,hasEdits:()=>$n,hasEmptyContent:()=>Zn,isEmailLoaded:()=>Un,isEmailSent:()=>Yn,isEmpty:()=>qn,isFeatureActive:()=>Hn,isSaving:()=>Wn,isSidebarOpened:()=>Gn});var a={};o.r(a),o.d(a,{getPersonalizationTagsList:()=>fs});const l=window.wp.data,c=window.wp.element,d=window.wp.hooks,m=(window.wp.formatLibrary,window.wp.blockLibrary),p=window.wp.blockEditor,u=window.wp.compose,_=window.ReactJSXRuntime,h=(0,u.createHigherOrderComponent)((e=>function(t){return"core/columns"!==t.name?(0,_.jsx)(e,{...t}):(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(e,{...t}),(0,_.jsx)(p.InspectorControls,{children:(0,_.jsx)("style",{children:"\n      .components-panel__body .components-toggle-control .components-form-toggle { opacity: 0.3; }\n      .components-panel__body .components-toggle-control .components-form-toggle__input { pointer-events: none; }\n      .components-panel__body .components-toggle-control label { pointer-events: none; }\n    "})})]})}),"columnsEditCallback"),g=window.wp.i18n;function x({layoutClassNames:e}){const t=(0,p.useBlockProps)({className:e});return(0,_.jsxs)("div",{...t,children:[(0,_.jsx)("p",{children:(0,g.__)("This is the Content block.","woocommerce")}),(0,_.jsx)("p",{children:(0,g.__)("It will display all the blocks in the email content, which might be only simple text paragraphs. You can enrich your message with images, incorporate data through tables, explore different layout designs with columns, or use any other block type.","woocommerce")})]})}const y=(0,u.createHigherOrderComponent)((e=>function(t){return"core/image"!==t.name?(0,_.jsx)(e,{...t}):(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(e,{...t}),(0,_.jsx)(p.InspectorControls,{children:(0,_.jsx)("style",{children:"\n        .components-tools-panel .components-toggle-control { display: none; }\n      "})})]})}),"imageEditCallback"),w=window.wp.richText,f=window.wp.components,v=(e,t)=>{const o=e.current.ownerDocument.defaultView.getSelection();if(!o.rangeCount)return{start:0,end:0};const n=o.getRangeAt(0);if(null===o.anchorNode.previousSibling)return{start:o.anchorOffset,end:o.anchorOffset+n.toString().length};const s=(0,w.create)({html:t});let r=o.anchorNode.previousSibling;r=function(e){let t=e;for(;t&&t?.children?.length>0;)t=t.children[0];return t}(r);const i=function(e,t){let o=null;for(const[n,s]of t.entries())if(s)for(const t of s)t?.attributes&&e.tagName.toLowerCase()===t.tagName?.toLowerCase()&&e.getAttribute("data-link-href")===t?.attributes["data-link-href"]&&(o=n);return o}(r,s.formats);if(null!==i)return{start:i+o.anchorOffset+1,end:i+o.anchorOffset+n.toString().length};const a=function(e,t){for(const[o,n]of t.entries()){if(!n)continue;const{attributes:t}=n;if(e.getAttribute("data-rich-text-comment")===t["data-rich-text-comment"])return o}return null}(r,s.replacements);return null!==a?{start:a+o.anchorOffset+1,end:a+o.anchorOffset+n.toString().length}:{start:s.text.length,end:s.text.length+n.toString().length}},b=(e,t)=>(t.forEach((t=>{if(!e.includes(t.token.slice(0,t.token.length-1)))return;const o=t.token.substring(1,t.token.length-1).replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),n=new RegExp(`(?<!\x3c!--)(?<!["'])\\[(${o}(\\s[^\\]]*)?)\\](?!--\x3e)`,"g");e=e.replace(n,(e=>`\x3c!--${e}--\x3e`))})),e),j=({groupedTags:e,activeCategory:t,onCategorySelect:o})=>{const n=e=>e===t?"woocommerce-personalization-tags-modal-menu-item-active":"";return(0,_.jsxs)(f.MenuGroup,{className:"woocommerce-personalization-tags-modal-menu",children:[(0,_.jsx)(f.MenuItem,{onClick:()=>o(null),className:n(null),children:(0,g.__)("All","woocommerce")}),(0,_.jsx)("div",{className:"woocommerce-personalization-tags-modal-menu-separator","aria-hidden":"true"}),Object.keys(e).map(((e,t,s)=>(0,_.jsxs)(c.Fragment,{children:[(0,_.jsx)(f.MenuItem,{onClick:()=>o(e),className:n(e),children:e}),t<s.length-1&&(0,_.jsx)("div",{className:"woocommerce-personalization-tags-modal-menu-separator","aria-hidden":"true"})]},e)))]})},S=({groupedTags:e,activeCategory:t,onInsert:o,canInsertLink:n,closeCallback:s,openLinkModal:r})=>{const i=null===t?Object.entries(e):[[t,e[t]||[]]];return(0,_.jsx)(_.Fragment,{children:i.map((([e,t])=>(0,_.jsxs)("div",{children:[(0,_.jsx)("div",{className:"woocommerce-personalization-tags-modal-category",children:e}),(0,_.jsx)("div",{className:"woocommerce-personalization-tags-modal-category-group",children:t.map((t=>(0,_.jsxs)("div",{className:"woocommerce-personalization-tags-modal-category-group-item",children:[(0,_.jsxs)("div",{className:"woocommerce-personalization-tags-modal-item-text",children:[(0,_.jsx)("strong",{children:t.name}),t.valueToInsert]}),(0,_.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"flex-end"},children:[(0,_.jsx)(f.Button,{variant:"link",onClick:()=>{o&&o(t.valueToInsert,!1)},children:(0,g.__)("Insert","woocommerce")}),e===(0,g.__)("Link","woocommerce")&&n&&(0,_.jsx)(_.Fragment,{children:(0,_.jsx)(f.Button,{variant:"link",onClick:()=>{s(),r(t)},children:(0,g.__)("Insert as link","woocommerce")})})]})]},t.token)))})]},e)))})},k=({onInsert:e,isOpened:t,closeCallback:o,tag:n})=>{const[s,r]=(0,c.useState)((0,g.__)("Link","woocommerce"));return t?(0,_.jsxs)(f.Modal,{size:"small",title:(0,g.__)("Insert Link","woocommerce"),onRequestClose:o,className:"woocommerce-personalization-tags-modal",children:[(0,_.jsx)(f.TextControl,{label:(0,g.__)("Link Text","woocommerce"),value:s,onChange:r}),(0,_.jsx)(f.Button,{isPrimary:!0,onClick:()=>{e&&e(n.token,s)},children:(0,g.__)("Insert","woocommerce")})]}):null},C=window.lodash,T=(0,d.applyFilters)("woocommerce_email_editor_events_tracking_enabled",!1),E="email_editor_events",N=new EventTarget,P=(e,t={})=>{if(!T)return;const o={name:`${E}_${e}`,..."object"!=typeof t?{data:t}:t};N.dispatchEvent(new CustomEvent(E,{detail:o}))},I=function(){const e={};return(t,o={})=>{if(!T)return;const n=`${t}_${JSON.stringify(o).length}`;e[n]||(P(t,o),e[n]=!0)}}(),M=(0,C.debounce)(P,700),B="email-editor/editor",A="document",F="block",R=window.WooCommerceEmailEditor.current_post_type,L=window.WooCommerceEmailEditor.current_post_id,z=({onInsert:e,isOpened:t,closeCallback:o,canInsertLink:n=!1,openedBy:s=""})=>{const[r,i]=(0,c.useState)(null),[a,d]=(0,c.useState)(""),[m,p]=(0,c.useState)(null),[u,h]=(0,c.useState)(!1),x=(0,l.useSelect)((e=>e(B).getPersonalizationTagsList()),[]);if(u)return(0,_.jsx)(k,{onInsert:(t,o)=>{e(t,o),h(!1)},isOpened:u,closeCallback:()=>h(!1),tag:m});if(!t)return null;I("personalization_tags_modal_opened",{openedBy:s});const y=x.reduce(((e,t)=>{const{category:o,name:n,token:s}=t;return(!a||n.toLowerCase().includes(a.toLowerCase())||s.toLowerCase().includes(a.toLowerCase()))&&(e[o]||(e[o]=[]),e[o].push(t)),e}),{});return(0,_.jsxs)(f.Modal,{size:"medium",title:(0,g.__)("Personalization Tags","woocommerce"),onRequestClose:()=>{o(),P("personalization_tags_modal_closed",{openedBy:s})},className:"woocommerce-personalization-tags-modal",children:[(0,_.jsxs)("p",{children:[(0,g.__)("Insert personalization tags to dynamically fill in information and personalize your emails.","woocommerce")," ",(0,_.jsx)(f.ExternalLink,{href:"https://kb.mailpoet.com/article/435-a-guide-to-personalisation-tags-for-tailored-newsletters#list",onClick:()=>P("personalization_tags_modal_learn_more_link_clicked",{openedBy:s}),children:(0,g.__)("Learn more","woocommerce")})]}),(0,_.jsx)(f.SearchControl,{onChange:e=>{d(e),I("personalization_tags_modal_search_control_input_updated",{openedBy:s})},value:a}),(0,_.jsx)(j,{groupedTags:y,activeCategory:r,onCategorySelect:e=>{i(e),P("personalization_tags_modal_category_menu_clicked",{category:e,openedBy:s})}}),(0,_.jsx)(S,{groupedTags:y,activeCategory:r,onInsert:t=>{e(t),P("personalization_tags_modal_tag_insert_button_clicked",{insertedTag:t,activeCategory:r,openedBy:s})},closeCallback:o,canInsertLink:n,openLinkModal:e=>{p(e),h(!0)}})]})},D=({contentRef:e,onUpdate:t})=>{const[o,n]=(0,c.useState)(!1),[s,r]=(0,c.useState)(null),[i,a]=(0,c.useState)(""),[l,d]=(0,c.useState)("");return(0,c.useEffect)((()=>{if(!e||!e.current)return;const t=e.current,o=e=>{const t=e.target.closest("span[data-rich-text-comment]");if(t){const e=t.innerText.replace(/^\[|\]$/g,"");d(e),a(e),r(t),n(!0)}};return t.addEventListener("click",o),()=>{t.removeEventListener("click",o)}}),[e]),(0,_.jsx)(_.Fragment,{children:o&&s&&(0,_.jsx)(f.Popover,{position:"bottom right",onClose:()=>n(!1),anchor:s,className:"woocommerce-personalization-tag-popover",children:(0,_.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content",children:[(0,_.jsx)(f.TextControl,{label:(0,g.__)("Personalization Tag","woocommerce"),value:i,onChange:e=>a(e),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),(0,_.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content-buttons",children:[(0,_.jsx)(f.Button,{isTertiary:!0,onClick:()=>{n(!1)},children:(0,g.__)("Cancel","woocommerce")}),(0,_.jsx)(f.Button,{isPrimary:!0,onClick:()=>{t(l,i),n(!1)},children:(0,g.__)("Update","woocommerce")})]})]})})})},O=({contentRef:e,onUpdate:t})=>{const[o,n]=(0,c.useState)(!1),[s,r]=(0,c.useState)(null),[i,a]=(0,c.useState)(""),[d,m]=(0,c.useState)(""),p=(0,l.useSelect)((e=>e(B).getPersonalizationTagsList()),[]);return(0,c.useEffect)((()=>{if(!e||!e.current)return;const t=e.current,o=e=>{const t=e.target.closest("a[data-link-href]");t&&(r(t),m(t.getAttribute("data-link-href")||""),a(t.textContent||""),n(!0))};return t.addEventListener("click",o),()=>{t.removeEventListener("click",o)}}),[e]),(0,_.jsx)(_.Fragment,{children:o&&s&&(0,_.jsx)(f.Popover,{position:"bottom left",onClose:()=>n(!1),anchor:s,className:"woocommerce-personalization-tag-popover",children:(0,_.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content",children:[(0,_.jsx)(f.TextControl,{label:(0,g.__)("Link Text","woocommerce"),value:i,onChange:e=>a(e),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,autoComplete:"off"}),(0,_.jsx)(f.SelectControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:(0,g.__)("Link tag","woocommerce"),value:d,onChange:e=>{m(e)},options:p.filter((e=>e.category===(0,g.__)("Link","woocommerce"))).map((e=>({label:e.name,value:e.token})))}),(0,_.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content-buttons",children:[(0,_.jsx)(f.Button,{isTertiary:!0,onClick:()=>{n(!1)},children:(0,g.__)("Cancel","woocommerce")}),(0,_.jsx)(f.Button,{isPrimary:!0,onClick:()=>{n(!1),t(s,d,i)},children:(0,g.__)("Update link","woocommerce")})]})]})})})};function V({contentRef:e}){const[t,o]=(0,c.useState)(!1),n=(0,l.useSelect)((e=>e("core/block-editor").getSelectedBlockClientId())),{updateBlockAttributes:s}=(0,l.useDispatch)("core/block-editor"),r=(0,l.useSelect)((e=>{const t=e("core/block-editor").getBlockAttributes(n);return t?.content?.originalHTML||t?.content||""})),i=(0,c.useCallback)(((t,o)=>{let{start:i,end:a}=v(e,r),l="";if(o){let e=(0,w.create)({html:r});e=(0,w.insert)(e,o,i,a),a=i+o.length,e=(0,w.applyFormat)(e,{type:"woocommerce-email-editor/link-shortcode",attributes:{"data-link-href":t,contenteditable:"false",style:"text-decoration: underline;"}},i,a),l=(0,w.toHTMLString)({value:e})}else{let e=(0,w.create)({html:r});e=(0,w.insert)(e,(0,w.create)({html:`\x3c!--${t}--\x3e&nbsp;`}),i,a),l=(0,w.toHTMLString)({value:e})}s(n,{content:l})}),[r,e,n,s]);return(0,_.jsx)(p.BlockControls,{children:(0,_.jsxs)(f.ToolbarGroup,{children:[(0,_.jsx)(f.ToolbarButton,{icon:"shortcode",title:(0,g.__)("Personalization Tags","woocommerce"),onClick:()=>{o(!0),P("block_controls_personalization_tags_button_clicked")}}),(0,_.jsx)(D,{contentRef:e,onUpdate:(e,t)=>{const o=r.replace(`\x3c!--[${e}]--\x3e`,`\x3c!--[${t}]--\x3e`);s(n,{content:o})}}),(0,_.jsx)(O,{contentRef:e,onUpdate:(e,t,o)=>{const i=e.getAttribute("data-link-href").replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),a=new RegExp(`<a([^>]*?)data-link-href="${i}"([^>]*?)>${e.textContent}</a>`,"gi"),l=r.replace(a,((e,n,s)=>`<a${n}data-link-href="${t}"${s}>${o}</a>`));s(n,{content:l})}}),(0,_.jsx)(z,{isOpened:t,onInsert:(e,t)=>{i(e,t),o(!1)},closeCallback:()=>o(!1),canInsertLink:!0,openedBy:"block-controls"})]})})}const H=(0,u.createHigherOrderComponent)((e=>t=>{const{attributes:o,setAttributes:n,name:s}=t,{content:r}=o,i=(0,l.useSelect)((e=>e(B).getPersonalizationTagsList()),[]),a=(0,c.useCallback)((()=>r?b(r,i):""),[r,i]),d=(0,c.useCallback)((e=>{if(void 0!==e.content){const t=b(e.content,i);n({...e,content:t})}else n(e)}),[i,n]);return"core/paragraph"===s||"core/heading"===s||"core/list-item"===s?(0,_.jsx)(e,{...t,attributes:{...o,content:a()},setAttributes:d}):(0,_.jsx)(e,{...t})}),"personalizationTagsLiveContentUpdate");var G=o(894),$=o.n(G);const U=window.wp.blocks,W=window.wp.primitives,q=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M9 9v6h11V9H9zM4 20h1.5V4H4v16z"})}),Z=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M12.5 15v5H11v-5H4V9h7V4h1.5v5h7v6h-7Z"})}),Y=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M4 15h11V9H4v6zM18.5 4v16H20V4h-1.5z"})}),J="__experimentalEmailFlexLayout";function X(e){return(0,U.hasBlockSupport)(e,J)}function K({justificationValue:e,onChange:t,isToolbar:o=!1}){const n=[{value:"left",icon:q,label:(0,g.__)("Justify items left","woocommerce")},{value:"center",icon:Z,label:(0,g.__)("Justify items center","woocommerce")},{value:"right",icon:Y,label:(0,g.__)("Justify items right","woocommerce")}];if(o){const o=n.map((e=>e.value));return(0,_.jsx)(p.JustifyContentControl,{value:e,onChange:t,allowedControls:o,popoverProps:{placement:"bottom-start"}})}return(0,_.jsx)(f.__experimentalToggleGroupControl,{__nextHasNoMarginBottom:!0,label:(0,g.__)("Justification","woocommerce"),value:e,onChange:t,className:"block-editor-hooks__flex-layout-justification-controls",children:n.map((({value:e,icon:t,label:o})=>(0,_.jsx)(f.__experimentalToggleGroupControlOptionIcon,{value:e,icon:t,label:o},e)))})}function Q({setAttributes:e,attributes:t,name:o}){if(!(0,U.getBlockSupport)(o,J,{}))return null;const{justifyContent:n="left"}=t.layout||{},s=o=>{e({layout:{...t.layout,justifyContent:o}})};return(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(p.InspectorControls,{children:(0,_.jsx)(f.PanelBody,{title:(0,g.__)("Layout","woocommerce"),children:(0,_.jsx)(f.Flex,{children:(0,_.jsx)(f.FlexItem,{children:(0,_.jsx)(K,{justificationValue:n,onChange:s})})})})}),(0,_.jsx)(p.BlockControls,{group:"block",__experimentalShareWithChildBlocks:!0,children:(0,_.jsx)(K,{justificationValue:n,onChange:s,isToolbar:!0})})]})}function ee(e){return X(e.name)?{...e,attributes:{...e.attributes,layout:{type:"object"}}}:e}const te=(0,u.createHigherOrderComponent)((e=>t=>[X(t.name)&&(0,_.jsx)(Q,{...t},"layout"),(0,_.jsx)(e,{...t},"edit")]),"withLayoutControls");function oe({block:e,props:t}){const{attributes:o}=t,{layout:n}=o,s=`is-content-justification-${n?.justifyContent||"left"}`,r=$()(s,"is-layout-email-flex is-layout-flex");return(0,_.jsx)(e,{...t,className:r})}const ne=(0,u.createHigherOrderComponent)((e=>function(t){return X(t.name)?(0,_.jsx)(oe,{block:e,props:t}):(0,_.jsx)(e,{...t})}),"withLayoutStyles"),se=window.wp.editor,re=window.wp.coreData,ie=window.wp.mediaUtils;function ae(e){var t,o,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(o=ae(e[t]))&&(n&&(n+=" "),n+=o)}else for(o in e)e[o]&&(n&&(n+=" "),n+=o);return n}const le=function(){for(var e,t,o=0,n="",s=arguments.length;o<s;o++)(e=arguments[o])&&(t=ae(e))&&(n&&(n+=" "),n+=t);return n},ce=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})}),de=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M11.776 4.454a.25.25 0 01.448 0l2.069 4.192a.25.25 0 00.188.137l4.626.672a.25.25 0 01.139.426l-3.348 3.263a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.362.263l-4.138-2.175a.25.25 0 00-.232 0l-4.138 2.175a.25.25 0 01-.363-.263l.79-4.607a.25.25 0 00-.071-.222L4.754 9.881a.25.25 0 01.139-.426l4.626-.672a.25.25 0 00.188-.137l2.069-4.192z"})}),me=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{fillRule:"evenodd",d:"M9.706 8.646a.25.25 0 01-.188.137l-4.626.672a.25.25 0 00-.139.427l3.348 3.262a.25.25 0 01.072.222l-.79 4.607a.25.25 0 00.362.264l4.138-2.176a.25.25 0 01.233 0l4.137 2.175a.25.25 0 00.363-.263l-.79-4.607a.25.25 0 01.072-.222l3.347-3.262a.25.25 0 00-.139-.427l-4.626-.672a.25.25 0 01-.188-.137l-2.069-4.192a.25.25 0 00-.448 0L9.706 8.646zM12 7.39l-.948 1.921a1.75 1.75 0 01-1.317.957l-2.12.308 1.534 1.495c.412.402.6.982.503 1.55l-.362 2.11 1.896-.997a1.75 1.75 0 011.629 0l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39z",clipRule:"evenodd"})}),pe=window.wp.viewport,ue=window.wp.preferences,_e=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})}),he=window.wp.deprecated;var ge=o.n(he);function xe(e){return["core/edit-post","core/edit-site"].includes(e)?(ge()(`${e} interface scope`,{alternative:"core interface scope",hint:"core/edit-post and core/edit-site are merging.",version:"6.6"}),"core"):e}function ye(e,t){return"core"===e&&"edit-site/template"===t?(ge()("edit-site/template sidebar",{alternative:"edit-post/document",version:"6.6"}),"edit-post/document"):"core"===e&&"edit-site/block-inspector"===t?(ge()("edit-site/block-inspector sidebar",{alternative:"edit-post/block",version:"6.6"}),"edit-post/block"):t}const we=(e,t)=>({type:"SET_DEFAULT_COMPLEMENTARY_AREA",scope:e=xe(e),area:t=ye(e,t)}),fe=(e,t)=>({registry:o,dispatch:n})=>{t&&(e=xe(e),t=ye(e,t),o.select(ue.store).get(e,"isComplementaryAreaVisible")||o.dispatch(ue.store).set(e,"isComplementaryAreaVisible",!0),n({type:"ENABLE_COMPLEMENTARY_AREA",scope:e,area:t}))},ve=e=>({registry:t})=>{e=xe(e),t.select(ue.store).get(e,"isComplementaryAreaVisible")&&t.dispatch(ue.store).set(e,"isComplementaryAreaVisible",!1)},be=(e,t)=>({registry:o})=>{if(!t)return;e=xe(e),t=ye(e,t);const n=o.select(ue.store).get(e,"pinnedItems");!0!==n?.[t]&&o.dispatch(ue.store).set(e,"pinnedItems",{...n,[t]:!0})},je=(e,t)=>({registry:o})=>{if(!t)return;e=xe(e),t=ye(e,t);const n=o.select(ue.store).get(e,"pinnedItems");o.dispatch(ue.store).set(e,"pinnedItems",{...n,[t]:!1})};function Se(e,t){return function({registry:o}){ge()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),o.dispatch(ue.store).toggle(e,t)}}function ke(e,t,o){return function({registry:n}){ge()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),n.dispatch(ue.store).set(e,t,!!o)}}function Ce(e,t){return function({registry:o}){ge()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),o.dispatch(ue.store).setDefaults(e,t)}}function Te(e){return{type:"OPEN_MODAL",name:e}}function Ee(){return{type:"CLOSE_MODAL"}}const Ne=(0,l.createRegistrySelector)((e=>(t,o)=>{o=xe(o);const n=e(ue.store).get(o,"isComplementaryAreaVisible");if(void 0!==n)return!1===n?null:t?.complementaryAreas?.[o]})),Pe=(0,l.createRegistrySelector)((e=>(t,o)=>{o=xe(o);const n=e(ue.store).get(o,"isComplementaryAreaVisible"),s=t?.complementaryAreas?.[o];return n&&void 0===s})),Ie=(0,l.createRegistrySelector)((e=>(t,o,n)=>{var s;n=ye(o=xe(o),n);const r=e(ue.store).get(o,"pinnedItems");return null===(s=r?.[n])||void 0===s||s})),Me=(0,l.createRegistrySelector)((e=>(t,o,n)=>(ge()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(ue.store).get(o,n))));function Be(e,t){return e.activeModal===t}const Ae=(0,l.combineReducers)({complementaryAreas:function(e={},t){switch(t.type){case"SET_DEFAULT_COMPLEMENTARY_AREA":{const{scope:o,area:n}=t;return e[o]?e:{...e,[o]:n}}case"ENABLE_COMPLEMENTARY_AREA":{const{scope:o,area:n}=t;return{...e,[o]:n}}}return e},activeModal:function(e=null,t){switch(t.type){case"OPEN_MODAL":return t.name;case"CLOSE_MODAL":return null}return e}}),Fe=(0,l.createReduxStore)("core/interface",{reducer:Ae,actions:n,selectors:s});(0,l.register)(Fe);const Re=(0,window.wp.plugins.withPluginContext)(((e,t)=>({icon:t.icon||e.icon,identifier:t.identifier||`${e.name}/${t.name}`}))),Le=Re((function({as:e=f.Button,scope:t,identifier:o,icon:n,selectedIcon:s,name:r,...i}){const a=e,c=(0,l.useSelect)((e=>e(Fe).getActiveComplementaryArea(t)===o),[o,t]),{enableComplementaryArea:d,disableComplementaryArea:m}=(0,l.useDispatch)(Fe);return(0,_.jsx)(a,{icon:s&&c?s:n,"aria-controls":o.replace("/",":"),onClick:()=>{c?m(t):d(t,o)},...i})})),ze=({smallScreenTitle:e,children:t,className:o,toggleButtonProps:n})=>{const s=(0,_.jsx)(Le,{icon:_e,...n});return(0,_.jsxs)(_.Fragment,{children:[(0,_.jsxs)("div",{className:"components-panel__header interface-complementary-area-header__small",children:[e&&(0,_.jsx)("h2",{className:"interface-complementary-area-header__small-title",children:e}),s]}),(0,_.jsxs)("div",{className:le("components-panel__header","interface-complementary-area-header",o),tabIndex:-1,children:[t,s]})]})},De=()=>{};function Oe({name:e,as:t=f.Button,onClick:o,...n}){return(0,_.jsx)(f.Fill,{name:e,children:({onClick:e})=>(0,_.jsx)(t,{onClick:o||e?(...t)=>{(o||De)(...t),(e||De)(...t)}:void 0,...n})})}Oe.Slot=function({name:e,as:t=f.ButtonGroup,fillProps:o={},bubblesVirtually:n,...s}){return(0,_.jsx)(f.Slot,{name:e,bubblesVirtually:n,fillProps:o,children:e=>{if(!c.Children.toArray(e).length)return null;const o=[];c.Children.forEach(e,(({props:{__unstableExplicitMenuItem:e,__unstableTarget:t}})=>{t&&e&&o.push(t)}));const n=c.Children.map(e,(e=>!e.props.__unstableExplicitMenuItem&&o.includes(e.props.__unstableTarget)?null:e));return(0,_.jsx)(t,{...s,children:n})}})};const Ve=Oe,He=({__unstableExplicitMenuItem:e,__unstableTarget:t,...o})=>(0,_.jsx)(f.MenuItem,{...o});function Ge({scope:e,target:t,__unstableExplicitMenuItem:o,...n}){return(0,_.jsx)(Le,{as:n=>(0,_.jsx)(Ve,{__unstableExplicitMenuItem:o,__unstableTarget:`${e}/${t}`,as:He,name:`${e}/plugin-more-menu`,...n}),role:"menuitemcheckbox",selectedIcon:ce,name:t,scope:e,...n})}function $e({scope:e,...t}){return(0,_.jsx)(f.Fill,{name:`PinnedItems/${e}`,...t})}$e.Slot=function({scope:e,className:t,...o}){return(0,_.jsx)(f.Slot,{name:`PinnedItems/${e}`,...o,children:e=>e?.length>0&&(0,_.jsx)("div",{className:le(t,"interface-pinned-items"),children:e})})};const Ue=$e,We={open:{width:280},closed:{width:0},mobileOpen:{width:"100vw"}};function qe({activeArea:e,isActive:t,scope:o,children:n,className:s,id:r}){const i=(0,u.useReducedMotion)(),a=(0,u.useViewportMatch)("medium","<"),l=(0,u.usePrevious)(e),d=(0,u.usePrevious)(t),[,m]=(0,c.useState)({});(0,c.useEffect)((()=>{m({})}),[t]);const p={type:"tween",duration:i||a||l&&e&&e!==l?0:.3,ease:[.6,0,.4,1]};return(0,_.jsx)(f.Fill,{name:`ComplementaryArea/${o}`,children:(0,_.jsx)(f.__unstableAnimatePresence,{initial:!1,children:(d||t)&&(0,_.jsx)(f.__unstableMotion.div,{variants:We,initial:"closed",animate:a?"mobileOpen":"open",exit:"closed",transition:p,className:"interface-complementary-area__fill",children:(0,_.jsx)("div",{id:r,className:s,style:{width:a?"100vw":280},children:n})})})})}const Ze=Re((function({children:e,className:t,closeLabel:o=(0,g.__)("Close plugin"),identifier:n,header:s,headerClassName:r,icon:i,isPinnable:a=!0,panelClassName:d,scope:m,name:p,smallScreenTitle:u,title:h,toggleShortcut:x,isActiveByDefault:y}){const[w,v]=(0,c.useState)(!1),{isLoading:b,isActive:j,isPinned:S,activeArea:k,isSmall:C,isLarge:T,showIconLabels:E}=(0,l.useSelect)((e=>{const{getActiveComplementaryArea:t,isComplementaryAreaLoading:o,isItemPinned:s}=e(Fe),{get:r}=e(ue.store),i=t(m);return{isLoading:o(m),isActive:i===n,isPinned:s(m,n),activeArea:i,isSmall:e(pe.store).isViewportMatch("< medium"),isLarge:e(pe.store).isViewportMatch("large"),showIconLabels:r("core","showIconLabels")}}),[n,m]);!function(e,t,o,n,s){const r=(0,c.useRef)(!1),i=(0,c.useRef)(!1),{enableComplementaryArea:a,disableComplementaryArea:d}=(0,l.useDispatch)(Fe);(0,c.useEffect)((()=>{n&&s&&!r.current?(d(e),i.current=!0):i.current&&!s&&r.current?(i.current=!1,a(e,t)):i.current&&o&&o!==t&&(i.current=!1),s!==r.current&&(r.current=s)}),[n,s,e,t,o,d,a])}(m,n,k,j,C);const{enableComplementaryArea:N,disableComplementaryArea:P,pinItem:I,unpinItem:M}=(0,l.useDispatch)(Fe);if((0,c.useEffect)((()=>{y&&void 0===k&&!C?N(m,n):void 0===k&&C&&P(m,n),v(!0)}),[k,y,m,n,C,N,P]),w)return(0,_.jsxs)(_.Fragment,{children:[a&&(0,_.jsx)(Ue,{scope:m,children:S&&(0,_.jsx)(Le,{scope:m,identifier:n,isPressed:j&&(!E||T),"aria-expanded":j,"aria-disabled":b,label:h,icon:E?ce:i,showTooltip:!E,variant:E?"tertiary":void 0,size:"compact"})}),p&&a&&(0,_.jsx)(Ge,{target:p,scope:m,icon:i,children:h}),(0,_.jsxs)(qe,{activeArea:k,isActive:j,className:le("interface-complementary-area",t),scope:m,id:n.replace("/",":"),children:[(0,_.jsx)(ze,{className:r,closeLabel:o,onClose:()=>P(m),smallScreenTitle:u,toggleButtonProps:{label:o,size:"small",shortcut:x,scope:m,identifier:n},children:s||(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)("h2",{className:"interface-complementary-area-header__title",children:h}),a&&(0,_.jsx)(f.Button,{className:"interface-complementary-area__pin-unpin-item",icon:S?de:me,label:S?(0,g.__)("Unpin from toolbar"):(0,g.__)("Pin to toolbar"),onClick:()=>(S?M:I)(m,n),isPressed:S,"aria-expanded":S,size:"compact"})]})}),(0,_.jsx)(f.Panel,{className:d,children:e})]})]})}));Ze.Slot=function({scope:e,...t}){return(0,_.jsx)(f.Slot,{name:`ComplementaryArea/${e}`,...t})};const Ye=Ze,Je=({isActive:e})=>((0,c.useEffect)((()=>{let e=!1;return document.body.classList.contains("sticky-menu")&&(e=!0,document.body.classList.remove("sticky-menu")),()=>{e&&document.body.classList.add("sticky-menu")}}),[]),(0,c.useEffect)((()=>(e?document.body.classList.add("is-fullscreen-mode"):document.body.classList.remove("is-fullscreen-mode"),()=>{e&&document.body.classList.remove("is-fullscreen-mode")})),[e]),null);function Xe({children:e,className:t,ariaLabel:o,as:n="div",...s}){return(0,_.jsx)(n,{className:le("interface-navigable-region",t),"aria-label":o,role:"region",tabIndex:"-1",...s,children:e})}const Ke={type:"tween",duration:.25,ease:[.6,0,.4,1]},Qe={hidden:{opacity:1,marginTop:-60},visible:{opacity:1,marginTop:0},distractionFreeHover:{opacity:1,marginTop:0,transition:{...Ke,delay:.2,delayChildren:.2}},distractionFreeHidden:{opacity:0,marginTop:-60},distractionFreeDisabled:{opacity:0,marginTop:0,transition:{...Ke,delay:.8,delayChildren:.8}}},et=(0,c.forwardRef)((function({isDistractionFree:e,footer:t,header:o,editorNotices:n,sidebar:s,secondarySidebar:r,content:i,actions:a,labels:l,className:d,enableRegionNavigation:m=!0,shortcuts:p},h){const[x,y]=(0,u.useResizeObserver)(),w=(0,u.useViewportMatch)("medium","<"),v={type:"tween",duration:(0,u.useReducedMotion)()?0:.25,ease:[.6,0,.4,1]},b=(0,f.__unstableUseNavigateRegions)(p);!function(e){(0,c.useEffect)((()=>{const t=document&&document.querySelector(`html:not(.${e})`);if(t)return t.classList.toggle(e),()=>{t.classList.toggle(e)}}),[e])}("interface-interface-skeleton__html-container");const j={header:(0,g._x)("Header","header landmark area"),body:(0,g.__)("Content"),secondarySidebar:(0,g.__)("Block Library"),sidebar:(0,g.__)("Settings"),actions:(0,g.__)("Publish"),footer:(0,g.__)("Footer"),...l};return(0,_.jsxs)("div",{...m?b:{},ref:(0,u.useMergeRefs)([h,m?b.ref:void 0]),className:le(d,"interface-interface-skeleton",b.className,!!t&&"has-footer"),children:[(0,_.jsxs)("div",{className:"interface-interface-skeleton__editor",children:[(0,_.jsx)(f.__unstableAnimatePresence,{initial:!1,children:!!o&&(0,_.jsx)(Xe,{as:f.__unstableMotion.div,className:"interface-interface-skeleton__header","aria-label":j.header,initial:e?"distractionFreeHidden":"hidden",whileHover:e?"distractionFreeHover":"visible",animate:e?"distractionFreeDisabled":"visible",exit:e?"distractionFreeHidden":"hidden",variants:Qe,transition:v,children:o})}),e&&(0,_.jsx)("div",{className:"interface-interface-skeleton__header",children:n}),(0,_.jsxs)("div",{className:"interface-interface-skeleton__body",children:[(0,_.jsx)(f.__unstableAnimatePresence,{initial:!1,children:!!r&&(0,_.jsx)(Xe,{className:"interface-interface-skeleton__secondary-sidebar",ariaLabel:j.secondarySidebar,as:f.__unstableMotion.div,initial:"closed",animate:w?"mobileOpen":"open",exit:"closed",variants:{open:{width:y.width},closed:{width:0},mobileOpen:{width:"100vw"}},transition:v,children:(0,_.jsxs)("div",{style:{position:"absolute",width:w?"100vw":"fit-content",height:"100%",right:0},children:[x,r]})})}),(0,_.jsx)(Xe,{className:"interface-interface-skeleton__content",ariaLabel:j.body,children:i}),!!s&&(0,_.jsx)(Xe,{className:"interface-interface-skeleton__sidebar",ariaLabel:j.sidebar,children:s}),!!a&&(0,_.jsx)(Xe,{className:"interface-interface-skeleton__actions",ariaLabel:j.actions,children:a})]})]}),!!t&&(0,_.jsx)(Xe,{className:"interface-interface-skeleton__footer",ariaLabel:j.footer,children:t})]})}));var tt=o(192),ot=o.n(tt);function nt(){const{globalStylePost:e}=(0,l.useSelect)((e=>({globalStylePost:e(B).getGlobalEmailStylesPost()})),[]),t=(0,c.useCallback)((t=>{e&&(0,l.dispatch)(re.store).editEntityRecord("postType","wp_global_styles",e.id,{styles:t.styles,settings:t.settings})}),[e]);return{userTheme:{settings:e?.settings,styles:e?.styles},updateUserTheme:t}}const st=window.wp.privateApis,{unlock:rt}=(0,st.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/edit-site"),{ExperimentalBlockCanvas:it}=rt(p.privateApis),{Tabs:at}=rt(f.privateApis),{ColorPanel:lt}=rt(p.privateApis),{useGlobalStylesOutputWithConfig:ct}=rt(p.privateApis);function dt(){const{userTheme:e}=nt(),{editorTheme:t}=(0,l.useSelect)((e=>({editorTheme:e(B).getTheme()})),[]),o=(0,c.useMemo)((()=>ot().all([{},t||{},e||{}])),[t,e]),[n]=ct(o);return[n||[]]}function mt(){const{hasEdits:e,autosaveInterval:t}=(0,l.useSelect)((e=>({hasEdits:e(B).hasEdits(),autosaveInterval:e(B).getAutosaveInterval()})),[]),{saveEditedEmail:o}=(0,l.useDispatch)(B);return(0,c.useEffect)((()=>{let n;return e&&t>0&&(n=setTimeout((()=>{o(),P("editor_content_auto_saved")}),1e3*t)),()=>{n&&clearTimeout(n)}}),[e,t,o]),null}const pt=(0,_.jsx)(W.SVG,{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-4 14.5H6c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h8v13zm4.5-.5c0 .3-.2.5-.5.5h-2.5v-13H18c.3 0 .5.2.5.5v12z"})});function ut(){const{isEditingTemplate:e}=(0,l.useSelect)((e=>({isEditingTemplate:"wp_template"===e(se.store).getCurrentPostType()})),[]);return[e?"template":"email"]}const _t=c.forwardRef((function(e,t){const[o]=ut();return(0,_.jsxs)(at.TabList,{ref:t,children:[(0,_.jsx)(at.Tab,{tabId:A,children:"template"===o?(0,g.__)("Template","woocommerce"):(0,g.__)("Email","woocommerce")}),(0,_.jsx)(at.Tab,{tabId:F,children:(0,g.__)("Block","woocommerce")})]})}));function ht({close:e}){I("edit_template_modal_opened");const{onNavigateToEntityRecord:t,template:o}=(0,l.useSelect)((e=>{const{getEditorSettings:t}=e(se.store);return{onNavigateToEntityRecord:t().onNavigateToEntityRecord,template:e(B).getCurrentTemplate()}}),[]);return(0,_.jsxs)(f.Modal,{size:"medium",onRequestClose:e,__experimentalHideHeader:!0,children:[(0,_.jsx)("p",{children:(0,g.__)("Note that the same template can be used by multiple emails, so any changes made here may affect other emails on the site. To switch back to editing the page content click the ‘Back’ button in the toolbar.","woocommerce")}),(0,_.jsxs)(f.Flex,{justify:"end",children:[(0,_.jsx)(f.FlexItem,{children:(0,_.jsx)(f.Button,{variant:"tertiary",onClick:()=>{P("edit_template_modal_cancel_button_clicked"),e()},children:(0,g.__)("Cancel","woocommerce")})}),(0,_.jsx)(f.FlexItem,{children:(0,_.jsx)(f.Button,{variant:"primary",onClick:()=>{P("edit_template_modal_continue_button_clicked",{templateId:o.id}),t({postId:o.id,postType:"wp_template"})},disabled:!o.id,children:(0,g.__)("Continue","woocommerce")})})]})]})}const gt=[];function xt(e,t){return e.map((e=>"core/post-content"===e.name?{...e,name:"core/group",innerBlocks:t}:e.innerBlocks?.length?{...e,innerBlocks:xt(e.innerBlocks,t)}:e))}const yt={};function wt(e=""){const{templates:t,patterns:o,emailPosts:n,hasEmailPosts:s}=(0,l.useSelect)((e=>{const t=e(B).getSentEmailEditorPosts();return{templates:e(B).getEmailTemplates(),patterns:e(B).getBlockPatternsForEmailTemplate(),emailPosts:t,hasEmailPosts:!(!t||!t?.length)}}),[]),r=(0,c.useMemo)((()=>{let n=[];const s=e&&(0,U.parse)(e);if(n=s?[{blocks:s}]:o,!n||!t)return gt;const r=[];return t?.filter((e=>"email-general"!==e.slug))?.forEach((e=>{n?.forEach((t=>{let o=(0,U.parse)(e.content?.raw);o=xt(o,t.blocks),r.push({id:e.id,slug:e.slug,previewContentParsed:o,emailParsed:t.blocks,template:e,category:"basic",type:e.type,displayName:t.title?`${e.title.rendered} - ${t.title}`:e.title.rendered})}))})),r}),[t,o,e]),i=(0,c.useMemo)((()=>n?.map((e=>{const t=(0,d.applyFilters)("woocommerce_email_editor_preferred_template_title","",e),{postTemplateContent:o}=function(e,t=[]){const o=e.template,n={postTemplateContent:null};if(!o)return n;if(yt[o])return yt[o];const s=t.find((e=>e.slug===o));if(!s)return n;const r={postTemplateContent:s?.template};return yt[o]=r,r}(e,r),n=(0,U.parse)(e.content?.raw);let s=n;o?.content?.raw&&(s=xt((0,U.parse)(o?.content?.raw),n));const i={...e,title:{raw:e.title.raw,rendered:t||e.title.rendered}};return{id:e.id,slug:e.slug,previewContentParsed:s,emailParsed:n,category:"recent",type:e.type,displayName:i.title.rendered,template:i}}))),[n,r]);return[r||gt,i||gt,s]}const ft=(0,c.forwardRef)((function({icon:e,size:t=24,...o},n){return(0,c.cloneElement)(e,{width:t,height:t,...o,ref:n})})),vt=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M19 8h-1V6h-5v2h-2V6H6v2H5c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2zm.5 10c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5v-8c0-.3.2-.5.5-.5h14c.3 0 .5.2.5.5v8z"})}),bt=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z"})}),jt=(0,window.wp.priorityQueue.createQueue)();function St({children:e,placeholder:t}){const[o,n]=(0,c.useState)(!1);return(0,c.useEffect)((()=>{const e={};return jt.add(e,(()=>{(0,c.flushSync)((()=>{n(!0)}))})),()=>{jt.cancel(e)}}),[]),o?e:t}function kt(){return(0,_.jsxs)("div",{className:"block-editor-inserter__no-results",children:[(0,_.jsx)(ft,{className:"block-editor-inserter__no-results-icon",icon:vt}),(0,_.jsx)("p",{children:(0,g.__)("No recent templates.","woocommerce")}),(0,_.jsx)("p",{children:(0,g.__)("Your recent creations will appear here as soon as you begin.","woocommerce")})]})}const Ct=(0,c.memo)((function({templates:e,onTemplateSelection:t,selectedCategory:o}){const{layout:n}=(0,l.useSelect)((e=>{const{getEditorSettings:t}=e(se.store);return{layout:t().__experimentalFeatures.layout}})),[s]=dt(),r=s.reduce(((e,t)=>{var o;return e+(null!==(o=t.css)&&void 0!==o?o:"")}),"")+`.is-root-container { width: ${n.contentSize}; margin: 0 auto; }`;return"recent"===o&&0===e.length?(0,_.jsx)(kt,{}):(0,_.jsx)("div",{className:"block-editor-block-patterns-list",role:"listbox",children:e.map((e=>(0,_.jsx)("div",{className:"block-editor-block-patterns-list__list-item email-editor-pattern__list-item",children:(0,_.jsx)("div",{className:"block-editor-block-patterns-list__item",role:"button",tabIndex:0,onClick:()=>{t(e)},onKeyPress:o=>{"Enter"!==o.key&&" "!==o.key||t(e)},children:(0,_.jsxs)(St,{placeholder:(0,_.jsx)("p",{children:(0,g.__)("rendering template","woocommerce")}),children:[(0,_.jsx)(p.BlockPreview,{blocks:e.previewContentParsed,viewportWidth:900,minHeight:300,additionalStyles:[{css:r}]}),(0,_.jsx)(f.__experimentalHStack,{className:"block-editor-patterns__pattern-details",children:(0,_.jsx)("h4",{className:"block-editor-block-patterns-list__item-title",children:e.displayName})})]})})},`${e.slug}_${e.displayName}_${e.id}`)))})}),((e,t)=>e.templates.length===t.templates.length&&e.selectedCategory===t.selectedCategory));function Tt({templates:e,onTemplateSelection:t,selectedCategory:o}){const n=(0,c.useMemo)((()=>e.filter((e=>e.category===o))),[o,e]);return(0,_.jsxs)("div",{className:"block-editor-block-patterns-explorer__list",children:["recent"===o&&(0,_.jsx)("div",{className:"email-editor-recent-templates-info",children:(0,_.jsxs)(f.__experimentalHStack,{spacing:1,expanded:!1,justify:"start",children:[(0,_.jsx)(ft,{icon:bt}),(0,_.jsx)("p",{children:(0,g.__)("Templates created on the legacy editor will not appear here.","woocommerce")})]})}),(0,_.jsx)(Ct,{templates:n,onTemplateSelection:t,selectedCategory:o})]})}function Et({selectedCategory:e,templateCategories:t,onClickCategory:o}){const n="block-editor-block-patterns-explorer__sidebar";return(0,_.jsx)("div",{className:n,children:(0,_.jsx)("div",{className:`${n}__categories-list`,children:t.map((({name:t,label:s})=>(0,_.jsx)(f.Button,{label:s,className:`${n}__categories-list__item`,isPressed:e===t,onClick:()=>{o(t)},children:s},t)))})})}const Nt=[{name:"recent",label:"Recent"},{name:"basic",label:"Basic"}],Pt=(0,c.memo)((function({hasEmailPosts:e,templates:t,handleTemplateSelection:o,templateSelectMode:n}){const[s,r]=(0,c.useState)(Nt[1].name),i="swap"===n,a=Nt.filter((({name:e})=>"recent"!==e||!i));return(0,c.useEffect)((()=>{setTimeout((()=>{e&&!i&&r(Nt[0].name)}),1e3)}),[e,i]),(0,_.jsxs)("div",{className:"block-editor-block-patterns-explorer",children:[(0,_.jsx)(Et,{templateCategories:a,selectedCategory:s,onClickCategory:e=>{P("template_select_modal_category_change",{category:e}),r(e)}}),(0,_.jsx)(Tt,{templates:t,onTemplateSelection:o,selectedCategory:s})]})}));function It({onSelectCallback:e,closeCallback:t=null,previewContent:o=""}){const n=o?"swap":"new";I("template_select_modal_opened",{templateSelectMode:n});const[s,r,i]=wt(o),a=s?.length>0,c=t=>{const s=t.type===R,r=t.template;P("template_select_modal_template_selected",{templateSlug:t.slug,templateSelectMode:n,templateType:t.type}),o||(0,l.dispatch)(se.store).resetEditorBlocks(t.emailParsed),(0,l.dispatch)(B).setTemplateToPost(s?r.template:t.slug),e()},d=()=>{var e;const t=null!==(e=s[0])&&void 0!==e?e:null;t&&(P("template_select_modal_handle_close_without_template_selected"),c(t))};return(0,_.jsxs)(f.Modal,{title:"new"===n?(0,g.__)("Start with an email preset","woocommerce"):(0,g.__)("Select a template","woocommerce"),onRequestClose:()=>(P("template_select_modal_closed",{templateSelectMode:n}),t?t():d()),isFullScreen:!0,children:[(0,_.jsx)(Pt,{hasEmailPosts:i,templates:[...s,...r],handleTemplateSelection:c,templateSelectMode:n}),(0,_.jsx)(f.Flex,{className:"email-editor-modal-footer",justify:"flex-end",children:(0,_.jsx)(f.FlexItem,{children:(0,_.jsx)(f.Button,{variant:"tertiary",className:"email-editor-start_from_scratch_button",onClick:()=>(P("template_select_modal_start_from_scratch_clicked"),d()),isBusy:!a,children:(0,g.__)("Start from scratch","woocommerce")})})})]})}function Mt(){const{template:e,currentEmailContent:t,canUpdateTemplates:o}=(0,l.useSelect)((e=>({template:e(B).getCurrentTemplate(),currentEmailContent:e(B).getEditedEmailContent(),canUpdateTemplates:e(B).canUserEditTemplates()})),[]),[n]=wt("swap"),[s,r]=(0,c.useState)(!1),[i,a]=(0,c.useState)(!1);return(0,_.jsxs)(_.Fragment,{children:[e&&(0,_.jsx)(f.PanelRow,{children:(0,_.jsxs)(f.Flex,{justify:"start",children:[(0,_.jsx)(f.FlexItem,{className:"editor-post-panel__row-label",children:(0,g.__)("Template","woocommerce")}),(0,_.jsxs)(f.FlexItem,{children:[!(n?.length>1||o)&&(0,_.jsx)("b",{children:e?.title}),(n?.length>1||o)&&(0,_.jsx)(f.DropdownMenu,{icon:null,text:e?.title,toggleProps:{variant:"tertiary"},label:(0,g.__)("Template actions","woocommerce"),onToggle:t=>P("sidebar_template_actions_clicked",{currentTemplate:e?.title,isOpen:t}),children:({onClose:e})=>(0,_.jsxs)(_.Fragment,{children:[o&&(0,_.jsx)(f.MenuItem,{onClick:()=>{P("sidebar_template_actions_edit_template_clicked"),r(!0),e()},children:(0,g.__)("Edit template","woocommerce")}),n?.length>1&&(0,_.jsx)(f.MenuItem,{onClick:()=>{P("sidebar_template_actions_swap_template_clicked"),a(!0),e()},children:(0,g.__)("Swap template","woocommerce")})]})})]})]})}),s&&(0,_.jsx)(ht,{close:()=>(P("edit_template_modal_closed"),r(!1))}),i&&(0,_.jsx)(It,{onSelectCallback:()=>a(!1),closeCallback:()=>a(!1),previewContent:t})]})}const Bt=(0,d.applyFilters)("woocommerce_email_editor_setting_sidebar_extension_component",(function({label:e,labelSuffix:t,help:o,placeholder:n,attributeName:s,attributeValue:r,updateProperty:i=()=>{}}){const[a,d]=(0,c.useState)(null),[m,u]=(0,c.useState)(!1),h=(0,l.useSelect)((e=>e(B).getPersonalizationTagsList()),[]),x=(0,c.useRef)(null),y=(0,c.useCallback)(((e,t,o)=>{var n,r;const a=null!==(n=o?.start)&&void 0!==n?n:t.length,l=null!==(r=o?.end)&&void 0!==r?r:t.length;let c=(0,w.create)({html:t});c=(0,w.insert)(c,(0,w.create)({html:`\x3c!--${e}--\x3e`}),a,l);const m=(0,w.toHTMLString)({value:c});i(s,m),d(null)}),[s,i]),j=(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)("span",{children:e}),(0,_.jsx)(f.Button,{className:"woocommerce-settings-panel-personalization-tags-button",icon:"shortcode",title:(0,g.__)("Personalization Tags","woocommerce"),onClick:()=>{u(!0),P("rich_text_with_button_personalization_tags_shortcode_icon_clicked",{attributeName:s,label:e})}}),t]});return s?(0,_.jsxs)(f.BaseControl,{id:"",label:j,className:`woocommerce-settings-panel-${s}-text`,help:o,__nextHasNoMarginBottom:!0,children:[(0,_.jsx)(z,{isOpened:m,onInsert:e=>{y(e,null!=r?r:"",a),u(!1),P("rich_text_with_button_personalization_tags_inserted",{attributeName:s,value:e})},closeCallback:()=>u(!1),openedBy:"RichTextWithButton-BaseControl"}),(0,_.jsx)(D,{contentRef:x,onUpdate:(e,t)=>{const o=(null!=r?r:"").replace(`\x3c!--[${e}]--\x3e`,`\x3c!--[${t}]--\x3e`);i(s,o)}}),(0,_.jsx)(p.RichText,{ref:x,className:"woocommerce-settings-panel-richtext",placeholder:n,onFocus:()=>{d(v(x,null!=r?r:""))},onKeyUp:()=>{d(v(x,null!=r?r:""))},onClick:()=>{d(v(x,null!=r?r:""))},onChange:e=>{var t;e=b(null!==(t=e)&&void 0!==t?t:"",h),i(s,e),I("rich_text_with_button_input_field_updated",{attributeName:s})},value:null!=r?r:"","data-automation-id":`email_${s}`})]}):null}));function At(){return(0,_.jsx)(f.PanelBody,{title:(0,g.__)("Settings","woocommerce"),className:"woocommerce-email-editor__settings-panel",onToggle:e=>P("settings_panel_body_toggle",{opened:e}),children:(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(Mt,{}),(0,_.jsx)(Bt,{})]})})}const Ft=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M4 6h12V4.5H4V6Zm16 4.5H4V9h16v1.5ZM4 15h16v-1.5H4V15Zm0 4.5h16V18H4v1.5Z"})}),Rt=(0,d.applyFilters)("woocommerce_email_editor_sidebar_email_type_info_icon",(()=>(0,_.jsx)(ft,{icon:Ft}))),Lt=(0,d.applyFilters)("woocommerce_email_editor_sidebar_email_type_info_content",(()=>(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)("h2",{children:(0,g.__)("Email content","woocommerce")}),(0,_.jsx)("span",{children:(0,g.__)("This block represents the main content of your email, such as the invoice or order details. When the email is sent, it will be replaced with the actual email content.","woocommerce")})]})));function zt(){return(0,_.jsx)(_.Fragment,{children:(0,_.jsx)(f.Panel,{className:"woocommerce-email-sidebar-email-type-info",children:(0,_.jsx)(f.PanelBody,{children:(0,_.jsxs)(f.PanelRow,{children:[(0,_.jsx)("span",{className:"woocommerce-email-type-info-icon",children:(0,_.jsx)(Rt,{})}),(0,_.jsx)("div",{className:"woocommerce-email-type-info-content",children:(0,_.jsx)(Lt,{})})]})})})})}function Dt(){return(0,_.jsxs)(f.Panel,{children:[(0,_.jsx)(zt,{}),(0,_.jsx)(At,{})]})}const Ot=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),Vt=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"})});function Ht({close:e}){const t=(0,l.useSelect)((e=>e(B).getCurrentTemplate()),[]),{revertAndSaveTemplate:o}=(0,l.useDispatch)(B);return(0,_.jsxs)(f.Modal,{size:"medium",onRequestClose:e,__experimentalHideHeader:!0,children:[(0,_.jsx)("p",{children:(0,g.__)("This will clear ANY and ALL template customization. All updates made to the template will be lost. Do you want to proceed?","woocommerce")}),(0,_.jsxs)(f.Flex,{justify:"end",children:[(0,_.jsx)(f.FlexItem,{children:(0,_.jsx)(f.Button,{variant:"tertiary",onClick:e,children:(0,g.__)("Cancel","woocommerce")})}),(0,_.jsx)(f.FlexItem,{children:(0,_.jsx)(f.Button,{variant:"primary",onClick:async()=>{await o(t),e()},children:(0,g.__)("Reset","woocommerce")})})]})]})}function Gt(){const e=(0,l.useSelect)((e=>e(B).getCurrentTemplate()),[]),[t,o]=(0,c.useState)(!1),n=e?.description||"";return(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(f.Panel,{className:"woocommerce-email-sidebar-email-type-info",children:(0,_.jsx)(f.PanelBody,{children:(0,_.jsxs)(f.PanelRow,{children:[(0,_.jsx)("span",{className:"woocommerce-email-type-info-icon",children:(0,_.jsx)(ft,{icon:Ot})}),(0,_.jsxs)("div",{className:"woocommerce-email-type-info-content",children:[(0,_.jsxs)("div",{className:"woocommerce-email-type-info-content-heading",children:[(0,_.jsx)("h2",{children:e?.title||(0,g.__)("Template","woocommerce")}),(0,_.jsx)(f.DropdownMenu,{icon:Vt,label:(0,g.__)("Template actions","woocommerce"),children:({onClose:e})=>(0,_.jsx)(f.MenuItem,{onClick:()=>{o(!0),e()},info:(0,g.__)("Reset to default to clear all customizations","woocommerce"),children:(0,g.__)("Reset","woocommerce")})})]}),n&&(0,_.jsx)("p",{children:n||""}),(0,_.jsx)("p",{children:(0,g.__)("Edit this template to be used across multiple emails.","woocommerce")})]})]})})}),t&&(0,_.jsx)(Ht,{close:()=>{o(!1)}})]})}function $t(){const e=(0,d.applyFilters)("woocommerce_email_editor_template_sections",[]);return 0===e.length?null:(0,_.jsx)(_.Fragment,{children:e.map((e=>(0,_.jsx)("div",{children:e.render()},e.id)))})}function Ut(){return(0,_.jsxs)(f.Panel,{children:[(0,_.jsx)(Gt,{}),(0,_.jsx)($t,{})]})}function Wt(e){const[t]=ut(),o=(0,c.useRef)(null),n=(0,c.useContext)(at.Context);return(0,_.jsx)(Ye,{identifier:"email-editor/editor/main",closeLabel:(0,g.__)("Close sidebar","woocommerce"),headerClassName:"editor-sidebar__panel-tabs",className:"edit-post-sidebar",header:(0,_.jsx)(at.Context.Provider,{value:n,children:(0,_.jsx)(_t,{ref:o})}),icon:pt,scope:B,isActiveByDefault:!0,...e,children:(0,_.jsxs)(at.Context.Provider,{value:n,children:[(0,_.jsx)(at.TabPanel,{tabId:A,children:"template"===t?(0,_.jsx)(Ut,{}):(0,_.jsx)(Dt,{})}),(0,_.jsx)(at.TabPanel,{tabId:F,children:(0,_.jsx)(p.BlockInspector,{})})]})})}const qt=(0,c.memo)((function(e){const{toggleSettingsSidebarActiveTab:t}=(0,l.useDispatch)(B),{activeTab:o,selectedBlockId:n}=(0,l.useSelect)((e=>({activeTab:e(B).getSettingsSidebarActiveTab(),selectedBlockId:e(p.store).getSelectedBlockClientId()})),[]);return(0,c.useEffect)((()=>{t(n?F:A)}),[n,t]),(0,_.jsx)(at,{selectedTabId:o||A,onSelect:e=>(P("sidebar_tab_selected",{tabKey:e}),t(e)),children:(0,_.jsx)(Wt,{...e})})})),Zt=e=>{const t=(0,U.getBlockSupport)(e,"background");return t&&!1!==t?.backgroundImage};function Yt(){const e=(0,l.useSelect)((e=>e("core/block-editor").getSelectedBlock()),[]),t=(0,U.hasBlockSupport)(e?.name,"border",!1)||(0,U.hasBlockSupport)(e?.name,"__experimentalBorder",!1);return(0,_.jsxs)(_.Fragment,{children:[t&&(0,_.jsx)(f.Fill,{name:"InspectorControlsBorder",children:(0,_.jsx)(f.Notice,{className:"woocommerce-grid-full-width",status:"warning",isDismissible:!1,children:(0,g.__)("Border display may vary or be unsupported in some email clients.","woocommerce")})}),Zt(e?.name)&&(0,_.jsx)(f.Fill,{name:"InspectorControlsBackground",children:(0,_.jsx)(f.Notice,{className:"woocommerce-grid-full-width",status:"warning",isDismissible:!1,children:(0,g.__)("Select a background color for email clients that do not support background images.","woocommerce")})})]})}const Jt=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"})}),Xt=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M18.3 11.7c-.6-.6-1.4-.9-2.3-.9H6.7l2.9-3.3-1.1-1-4.5 5L8.5 16l1-1-2.7-2.7H16c.5 0 .9.2 1.3.5 1 1 1 3.4 1 4.5v.3h1.5v-.2c0-1.5 0-4.3-1.5-5.7z"})}),Kt=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M15.6 6.5l-1.1 1 2.9 3.3H8c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.3-.5h9.2L14.5 15l1.1 1.1 4.6-4.6-4.6-5z"})}),Qt=(0,_.jsx)(W.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,_.jsx)(W.Path,{d:"M3 6h11v1.5H3V6Zm3.5 5.5h11V13h-11v-1.5ZM21 17H10v1.5h11V17Z"})}),eo=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M6.6 6L5.4 7l4.5 5-4.5 5 1.1 1 5.5-6-5.4-6zm6 0l-1.1 1 4.5 5-4.5 5 1.1 1 5.5-6-5.5-6z"})}),to=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M11.6 7l-1.1-1L5 12l5.5 6 1.1-1L7 12l4.6-5zm6 0l-1.1-1-5.5 6 5.5 6 1.1-1-4.6-5 4.6-5z"})}),oo=window.wp.keycodes,no=window.wp.notices;function so({onClose:e,onRemove:t,postId:o}){const{getLastEntityDeleteError:n}=(0,l.useSelect)(re.store),{deleteEntityRecord:s}=(0,l.useDispatch)(re.store),{createErrorNotice:r}=(0,l.useDispatch)(no.store),i=()=>{e()};return(0,_.jsxs)(f.Modal,{className:"woocommerce-move-to-trash-modal",title:(0,g.__)("Move to trash","woocommerce"),onRequestClose:()=>{i(),P("trash_modal_closed")},focusOnMount:!0,children:[(0,_.jsx)("p",{children:(0,g.__)("Are you sure you want to move this email to trash?","woocommerce")}),(0,_.jsxs)("div",{className:"woocommerce-send-preview-modal-footer",children:[(0,_.jsx)(f.Button,{variant:"tertiary",onClick:()=>{i(),P("trash_modal_cancel_button_clicked")},children:(0,g.__)("Cancel","woocommerce")}),(0,_.jsx)(f.Button,{variant:"primary",onClick:async()=>{if(P("trash_modal_move_to_trash_button_clicked"),await s("postType",R,o,{},{throwOnError:!1}))t();else{const e=n("postType",R,o);if(410===e?.code)t();else{const t=e?.message?e.message:(0,g.__)("An error occurred while moving the email to the trash.","woocommerce");P("trash_modal_move_to_trash_error",{errorMessage:t}),await r(t,{type:"snackbar",isDismissible:!0,context:"email-editor"})}}},children:(0,g.__)("Move to trash","woocommerce")})]})]})}function ro(){const[e,t]=(0,c.useState)(!1),{urls:o,postId:n}=(0,l.useSelect)((e=>({urls:e(B).getUrls(),postId:e(B).getEmailPostId()})),[]),[s,r]=(0,re.useEntityProp)("postType",R,"status"),{saveEditedEmail:i}=(0,l.useDispatch)(B);return(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(f.DropdownMenu,{className:"edit-site-more-menu",popoverProps:{className:"edit-site-more-menu__content"},icon:Vt,label:(0,g.__)("More","woocommerce"),onToggle:e=>P("header_more_menu_dropdown_toggle",{isOpened:e}),children:({onClose:e})=>(0,_.jsxs)(_.Fragment,{children:[(0,_.jsxs)(f.MenuGroup,{label:(0,g._x)("View","noun","woocommerce"),children:[(0,_.jsx)(ue.PreferenceToggleMenuItem,{scope:"core",name:"fixedToolbar",label:(0,g.__)("Top toolbar","woocommerce"),info:(0,g.__)("Access all block and document tools in a single place","woocommerce"),messageActivated:(0,g.__)("Top toolbar activated","woocommerce"),messageDeactivated:(0,g.__)("Top toolbar deactivated","woocommerce"),onToggle:()=>P("header_more_menu_fixed_toolbar_toggle")}),(0,_.jsx)(ue.PreferenceToggleMenuItem,{scope:"core",name:"focusMode",label:(0,g.__)("Spotlight mode","woocommerce"),info:(0,g.__)("Focus at one block at a time","woocommerce"),messageActivated:(0,g.__)("Spotlight mode activated","woocommerce"),messageDeactivated:(0,g.__)("Spotlight mode deactivated","woocommerce"),onToggle:()=>P("header_more_menu_focus_mode_toggle")}),(0,_.jsx)(ue.PreferenceToggleMenuItem,{scope:B,name:"fullscreenMode",label:(0,g.__)("Fullscreen mode","woocommerce"),info:(0,g.__)("Work without distraction","woocommerce"),messageActivated:(0,g.__)("Fullscreen mode activated","woocommerce"),messageDeactivated:(0,g.__)("Fullscreen mode deactivated","woocommerce"),shortcut:oo.displayShortcut.secondary("f"),onToggle:()=>P("header_more_menu_fullscreen_mode_toggle")})]}),(0,_.jsx)(f.MenuGroup,{children:"trash"===s?(0,_.jsx)(f.MenuItem,{onClick:async()=>{await r("draft"),await i(),P("header_more_menu_restore_from_trash_button_clicked")},children:(0,g.__)("Restore from trash","woocommerce")}):(0,_.jsx)(f.MenuItem,{onClick:()=>{t(!0),P("header_more_menu_move_to_trash_button_clicked"),e()},isDestructive:!0,children:(0,g.__)("Move to trash","woocommerce")})})]})}),e&&(0,_.jsx)(so,{onClose:()=>t(!1),onRemove:()=>{window.location.href=o.listings},postId:n})]})}const io=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M15 4H9c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h6c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm.5 14c0 .3-.2.5-.5.5H9c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h6c.3 0 .5.2.5.5v12zm-4.5-.5h2V16h-2v1.5z"})}),ao=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M20.5 16h-.7V8c0-1.1-.9-2-2-2H6.2c-1.1 0-2 .9-2 2v8h-.7c-.8 0-1.5.7-1.5 1.5h20c0-.8-.7-1.5-1.5-1.5zM5.7 8c0-.3.2-.5.5-.5h11.6c.3 0 .5.2.5.5v7.6H5.7V8z"})}),lo=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})}),co=window.wp.url;let mo=function(e){return e.SUCCESS="success",e.ERROR="error",e}({});const po=(0,d.applyFilters)("woocommerce_email_editor_check_sending_method_configuration_link",`https://www.mailpoet.com/blog/mailpoet-smtp-plugin/?utm_source=woocommerce_email_editor&utm_medium=plugin&utm_source_platform=${R}`),uo=(0,c.memo)((function(){const e=(0,c.useRef)(null),{requestSendingNewsletterPreview:t,togglePreviewModal:o,updateSendPreviewEmail:n}=(0,l.useDispatch)(B),{toEmail:s,isSendingPreviewEmail:r,sendingPreviewStatus:i,isModalOpened:a,errorMessage:d}=(0,l.useSelect)((e=>e(B).getPreviewState()),[]),m=()=>{t(s)},p=()=>{P("send_preview_email_modal_closed"),o(!1)};return(0,c.useEffect)((()=>{a&&(e.current?.focus(),P("send_preview_email_modal_opened"))}),[a]),a?(0,_.jsxs)(f.Modal,{className:"woocommerce-send-preview-email",title:(0,g.__)("Send a test email","woocommerce"),onRequestClose:p,focusOnMount:!1,children:[i===mo.ERROR?(0,_.jsxs)("div",{className:"woocommerce-send-preview-modal-notice-error",children:[(0,_.jsx)("p",{children:(0,g.__)("Sorry, we were unable to send this email.","woocommerce")}),(0,_.jsx)("strong",{children:d&&(0,g.sprintf)((0,g.__)("Error: %s","woocommerce"),d)}),(0,_.jsxs)("ul",{children:[(0,_.jsx)("li",{children:po&&(0,c.createInterpolateElement)((0,g.__)("Please check your <link>sending method configuration</link> with your hosting provider.","woocommerce"),{link:(0,_.jsx)("a",{href:po,target:"_blank",rel:"noopener noreferrer",onClick:()=>P("send_preview_email_modal_check_sending_method_configuration_link_clicked")})})}),(0,_.jsx)("li",{children:(0,c.createInterpolateElement)((0,g.__)("Or, sign up for MailPoet Sending Service to easily send emails. <link>Sign up for free</link>","woocommerce"),{link:(0,_.jsx)("a",{href:`https://account.mailpoet.com/?s=1&g=1&utm_source=woocommerce_email_editor&utm_medium=plugin&utm_source_platform=${R}`,target:"_blank",rel:"noopener noreferrer",onClick:()=>P("send_preview_email_modal_sign_up_for_mailpoet_sending_service_link_clicked")},"sign-up-for-free")})})]})]}):null,(0,_.jsx)("p",{children:(0,c.createInterpolateElement)((0,g.__)("Send yourself a test email to test how your email would look like in different email apps. You can also test your spam score by sending a test email to <link1>{$serviceName}</link1>. <link2>Learn more</link2>.","woocommerce").replace("{$serviceName}","Mail Tester"),{link1:(0,_.jsx)("a",{href:"https://www.mail-tester.com/",target:"_blank",rel:"noopener noreferrer",onClick:()=>P("send_preview_email_modal_send_test_email_to_mail_tester_link_clicked")}),link2:(0,_.jsx)("a",{href:"https://kb.mailpoet.com/article/147-test-your-spam-score-with-mail-tester",target:"_blank",rel:"noopener noreferrer",onClick:()=>P("send_preview_email_modal_learn_more_about_mail_tester_link_clicked")})})}),(0,_.jsx)(f.TextControl,{label:(0,g.__)("Send to","woocommerce"),onChange:e=>{n(e),I("send_preview_email_modal_send_to_field_updated")},onKeyDown:e=>{const{keyCode:t}=e;t===oo.ENTER&&(e.preventDefault(),m(),P("send_preview_email_modal_send_to_field_key_code_enter"))},value:s,type:"email",ref:e,required:!0}),i===mo.SUCCESS?(0,_.jsxs)("p",{className:"woocommerce-send-preview-modal-notice-success",children:[(0,_.jsx)(ft,{icon:ce,style:{fill:"#4AB866"}}),(0,g.__)("Test email sent successfully!","woocommerce")]}):null,(0,_.jsxs)("div",{className:"woocommerce-send-preview-modal-footer",children:[(0,_.jsx)(f.Button,{variant:"tertiary",onClick:()=>{P("send_preview_email_modal_close_button_clicked"),p()},children:(0,g.__)("Close","woocommerce")}),(0,_.jsx)(f.Button,{variant:"primary",onClick:()=>{m(),P("send_preview_email_modal_send_test_email_button_clicked")},disabled:r||!(0,co.isEmail)(s),children:r?(0,g.__)("Sending…","woocommerce"):(0,g.__)("Send test email","woocommerce")})]})]}):null}));function _o(){const e=(0,l.useSelect)((e=>e(B).getDeviceType()),[]),{changePreviewDeviceType:t,togglePreviewModal:o}=(0,l.useDispatch)(B),n=e=>{t(e)},[s]=ut(),r={mobile:io,desktop:ao};return(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(f.DropdownMenu,{className:"woocommerce-preview-dropdown",label:(0,g.__)("Preview","woocommerce"),icon:r[e.toLowerCase()],onToggle:e=>P("header_preview_dropdown_clicked",{isOpened:e}),children:({onClose:t})=>(0,_.jsxs)(_.Fragment,{children:[(0,_.jsxs)(f.MenuGroup,{children:[(0,_.jsx)(f.MenuItem,{className:"block-editor-post-preview__button-resize",onClick:()=>{n("Desktop"),P("header_preview_dropdown_desktop_selected")},icon:"Desktop"===e&&ce,children:(0,g.__)("Desktop","woocommerce")}),(0,_.jsx)(f.MenuItem,{className:"block-editor-post-preview__button-resize",onClick:()=>{n("Mobile"),P("header_preview_dropdown_mobile_selected")},icon:"Mobile"===e&&ce,children:(0,g.__)("Mobile","woocommerce")})]}),"email"===s&&(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(f.MenuGroup,{children:(0,_.jsx)(f.MenuItem,{className:"block-editor-post-preview__button-resize",onClick:()=>{o(!0),P("header_preview_dropdown_send_test_email_selected"),t()},children:(0,g.__)("Send a test email","woocommerce")})}),(0,_.jsx)(f.MenuGroup,{children:(0,_.jsx)("div",{className:"edit-post-header-preview__grouping-external",children:(0,_.jsx)(se.PostPreviewButton,{role:"menuitem",forceIsAutosaveable:!0,"aria-label":(0,g.__)("Preview in new tab","woocommerce"),textContent:(0,_.jsxs)(_.Fragment,{children:[(0,g.__)("Preview in new tab","woocommerce"),(0,_.jsx)(ft,{icon:lo})]}),onPreview:()=>{P("header_preview_dropdown_preview_in_new_tab_selected"),t()}})})})," "]})]})}),(0,_.jsx)(uo,{})]})}const ho=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M17.3 10.1c0-2.5-2.1-4.4-4.8-4.4-2.2 0-4.1 1.4-4.6 3.3h-.2C5.7 9 4 10.7 4 12.8c0 2.1 1.7 3.8 3.7 3.8h9c1.8 0 3.2-1.5 3.2-3.3.1-1.6-1.1-2.9-2.6-3.2zm-.5 5.1h-9c-1.2 0-2.2-1.1-2.2-2.3s1-2.4 2.2-2.4h1.3l.3-1.1c.4-1.3 1.7-2.2 3.2-2.2 1.8 0 3.3 1.3 3.3 2.9v1.3l1.3.2c.8.1 1.4.9 1.4 1.8-.1 1-.9 1.8-1.8 1.8z"})});function go(){const{saveEditedEmail:e}=(0,l.useDispatch)(B),{hasEdits:t,isEmpty:o,isSaving:n}=(0,l.useSelect)((e=>({hasEdits:e(B).hasEdits(),isEmpty:e(B).isEmpty(),isSaving:e(B).isSaving()})),[]),s=!o&&!n&&!t,r=!t&&(o||n||s);let i=(0,g.__)("Save Draft","woocommerce");return s?i=(0,g.__)("Saved","woocommerce"):n&&(i=(0,g.__)("Saving","woocommerce")),(0,_.jsxs)(f.Button,{variant:"tertiary",disabled:r,onClick:()=>{e(),P("header_save_email_button_clicked",{label:i,isSaving:n,isSaved:s})},children:[n&&(0,_.jsx)(ft,{icon:ho}),s&&(0,_.jsx)(ft,{icon:ce}),i]})}const xo=(0,_.jsx)(W.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,_.jsx)(W.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})});function yo(){const{showIconLabels:e}=(0,l.useSelect)((e=>({showIconLabels:e(B).isFeatureActive("showIconLabels"),postId:e(B).getEmailPostId()})),[]),[t="",o]=(0,re.useEntityProp)("postType",R,"title"),n=(0,c.useRef)(null);return(0,_.jsx)("div",{ref:n,className:"woocommerce-email-editor-campaign-name",children:(0,_.jsx)(f.Dropdown,{popoverProps:{placement:"bottom",anchor:n.current},contentClassName:"woocommerce-email-editor-campaign-name-dropdown",renderToggle:({isOpen:o,onToggle:n})=>(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(f.Button,{onClick:()=>{n(),P("header_campaign_name_email_title_clicked",{isOpen:o})},className:"woocommerce-email-campaign-name-link",children:(0,_.jsxs)(f.__experimentalText,{size:"body",as:"h1",children:[(0,_.jsx)(f.VisuallyHidden,{as:"span",children:(0,g.__)("Editing email:","woocommerce")}),t]})}),(0,_.jsx)(f.Button,{className:"woocommerce-email-campaign-name__toggle",icon:xo,"aria-expanded":o,"aria-haspopup":"true",onClick:()=>{n(),P("header_campaign_name_toggle_icon_clicked",{isOpen:o})},label:(0,g.__)("Change campaign name","woocommerce"),children:e&&(0,g.__)("Rename","woocommerce")})]}),renderContent:()=>(0,_.jsx)("div",{className:"woocommerce-email-editor-email-title-edit",children:(0,_.jsx)(f.TextControl,{label:(0,g.__)("Campaign name","woocommerce"),value:t,onChange:e=>{o(e),I("header_campaign_name_title_updated")},name:"campaign_name",help:(0,g.__)("Name your email campaign to indicate its purpose. This would only be visible to you and not shown to your subscribers.","woocommerce")})})})})}function wo({validateContent:e,isContentInvalid:t}){const{isDirty:o}=(0,se.useEntitiesSavedStatesIsDirty)(),{hasEmptyContent:n,isEmailSent:s,urls:r}=(0,l.useSelect)((e=>({hasEmptyContent:e(B).hasEmptyContent(),isEmailSent:e(B).isEmailSent(),urls:e(B).getUrls()})),[]);function i(){r.send&&(window.location.href=r.send)}const a=n||s||t||o,c=(0,d.applyFilters)("woocommerce_email_editor_send_button_label",(0,g.__)("Send","woocommerce"));return(0,_.jsx)(f.Button,{variant:"primary",onClick:()=>{P("header_send_button_clicked"),e()&&(0,d.applyFilters)("woocommerce_email_editor_send_action_callback",i)()},disabled:a,"data-automation-id":"email_editor_send_button",children:c})}const fo=({onToggle:e})=>((0,c.useEffect)((()=>{document.querySelectorAll(".woocommerce-email-editor-save-button-dropdown  .components-panel__body").forEach((e=>{const t=e.querySelector(".components-panel__body-title button");if(t&&t.textContent.trim()===(0,g.__)("Templates","woocommerce")){const t=e.querySelectorAll(".components-panel__row");t.length&&(t[0].textContent=(0,g.__)("This change will affect emails that use this template.","woocommerce"))}}))}),[]),(0,_.jsx)(se.EntitiesSavedStates,{close:e}));function vo({validateContent:e,isDisabled:t}){const{isSaving:o}=(0,l.useSelect)((e=>({isSaving:e(B).isSaving()})),[]),n=(0,c.useRef)(null);let s=(0,g.__)("Save","woocommerce");return o&&(s=(0,g.__)("Saving","woocommerce")),(0,_.jsx)("div",{ref:n,children:(0,_.jsx)(f.Dropdown,{popoverProps:{placement:"bottom",anchor:n.current},contentClassName:"woocommerce-email-editor-save-button-dropdown",renderToggle:({onToggle:n})=>(0,_.jsx)(f.Button,{onClick:()=>{P("header_save_all_button_save_button_clicked"),e()&&n()},variant:"primary",disabled:o||t,children:s}),renderContent:({onToggle:e})=>(0,_.jsx)(fo,{onToggle:e})})})}const bo=window.wp.isShallowEqual;var jo=o.n(bo);function So(e){const t=(0,c.useRef)(e);return jo()(e,t.current)||(t.current=e),t.current}const ko=()=>{const e="email-validation",t=(0,l.useSelect)((t=>t(no.store).getNotices(e)));return{notices:t,hasValidationNotice:(0,c.useCallback)((e=>e?void 0!==t.find((t=>t.id===e)):t?.length>0),[t]),addValidationNotice:(0,c.useCallback)(((t,o,n=[])=>{(0,l.dispatch)(no.store).createNotice("error",o,{id:t,isDismissible:!1,actions:n,context:e})}),[e]),removeValidationNotice:(0,c.useCallback)((t=>{(0,l.dispatch)(no.store).removeNotice(t,e)}),[e])}},Co=[],To=f.ToolbarItem,Eo=p.BlockToolbar;function No(){const e=(0,c.useRef)(),t=(0,c.useRef)(),o=(0,c.useRef)(),n=(0,c.useRef)(),[s,r]=(0,c.useState)(!1),{undo:i,redo:a}=(0,l.useDispatch)(re.store),{setIsInserterOpened:m,setIsListViewOpened:u}=(0,l.useDispatch)(se.store),{isInserterSidebarOpened:h,isListviewSidebarOpened:x,isFixedToolbarActive:y,isBlockSelected:w,hasUndo:v,hasRedo:b}=(0,l.useSelect)((e=>({isInserterSidebarOpened:e(se.store).isInserterOpened(),isListviewSidebarOpened:e(se.store).isListViewOpened(),isFixedToolbarActive:e(ue.store).get("core","fixedToolbar"),isBlockSelected:!!e(p.store).getBlockSelectionStart(),hasUndo:e(re.store).hasUndo(),hasRedo:e(re.store).hasRedo()})),[]),[j]=ut(),{validateContent:S,isInvalid:k}=(()=>{const{addValidationNotice:e,hasValidationNotice:t,removeValidationNotice:o}=ko(),{editedContent:n,editedTemplateContent:s}=(0,l.useSelect)((e=>({editedContent:e(B).getEditedEmailContent(),editedTemplateContent:e(B).getCurrentTemplateContent()}))),r=(0,d.applyFilters)("woocommerce_email_editor_content_validation_rules",Co),i=So(n),a=So(s),m=(0,c.useCallback)((()=>{let n=!0;return r.forEach((({id:s,testContent:r,message:l,actions:c})=>{r(i+a)?(e(s,l,c),n=!1):t(s)&&o(s)})),n}),[i,a,e,o,t,r]);return(0,l.subscribe)((()=>{t()&&m()}),re.store),{isInvalid:t(),validateContent:m}})(),{dirtyEntityRecords:C}=(0,se.useEntitiesSavedStatesIsDirty)(),T=C.some((e=>e.name!==R)),E=e=>{e.preventDefault()},N=h?(0,g.__)("Close","woocommerce"):(0,g.__)("Add","woocommerce");return(0,_.jsxs)("div",{className:"editor-header edit-post-header",children:[(0,_.jsxs)("div",{className:"editor-header__toolbar",children:[(0,_.jsx)(p.NavigableToolbar,{className:"editor-document-tools edit-post-header-toolbar is-unstyled","aria-label":(0,g.__)("Email document tools","woocommerce"),children:(0,_.jsxs)("div",{className:"editor-document-tools__left",children:[(0,_.jsx)(To,{ref:e,as:f.Button,className:"editor-header-toolbar__inserter-toggle edit-post-header-toolbar__inserter-toggle",variant:"primary",isPressed:h,onMouseDown:E,onClick:()=>h?(P("header_inserter_sidebar_closed"),m(!1)):(P("header_inserter_sidebar_opened"),m(!0)),disabled:!1,icon:Jt,label:N,showTooltip:!0,"aria-expanded":h}),(0,_.jsx)(To,{ref:o,as:f.Button,className:"editor-history__undo",isPressed:!1,onMouseDown:E,onClick:()=>{i(),P("header_undo_icon_clicked")},disabled:!v,icon:Xt,label:(0,g.__)("Undo","woocommerce"),showTooltip:!0}),(0,_.jsx)(To,{ref:n,as:f.Button,className:"editor-history__redo",isPressed:!1,onMouseDown:E,onClick:()=>{a(),P("header_redo_icon_clicked")},disabled:!b,icon:Kt,label:(0,g.__)("Redo","woocommerce"),showTooltip:!0}),(0,_.jsx)(To,{ref:t,as:f.Button,className:"editor-header-toolbar__document-overview-toggle edit-post-header-toolbar__document-overview-toggle",isPressed:x,onMouseDown:E,onClick:()=>x?(P("header_listview_sidebar_closed"),u(!1)):(P("header_listview_sidebar_opened"),u(!0)),disabled:!1,icon:Qt,label:(0,g.__)("List view","woocommerce"),showTooltip:!0,"aria-expanded":h})]})}),y&&w&&(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)("div",{className:$()("editor-collapsible-block-toolbar",{"is-collapsed":s}),children:(0,_.jsx)(Eo,{hideDragHandle:!0})}),(0,_.jsx)(f.Button,{className:"editor-header__block-tools-toggle edit-post-header__block-tools-toggle",icon:s?eo:to,onClick:()=>{r((e=>!e)),P("header_blocks_tool_button_clicked",{isBlockToolsCollapsed:s})},label:s?(0,g.__)("Show block tools","woocommerce"):(0,g.__)("Hide block tools","woocommerce")})]})]}),(!y||!w||s)&&(0,_.jsx)("div",{className:"editor-header__center edit-post-header__center",children:"template"===j?(0,_.jsx)(se.DocumentBar,{}):(0,_.jsx)(yo,{})}),(0,_.jsxs)("div",{className:"editor-header__settings edit-post-header__settings",children:["email"===j&&(0,_.jsx)(go,{}),(0,_.jsx)(_o,{}),T||"template"===j&&C.length?(0,_.jsx)(vo,{validateContent:S,isDisabled:0===C.length}):(0,_.jsx)(wo,{validateContent:S,isContentInvalid:k}),(0,_.jsx)(Ue.Slot,{scope:B}),(0,_.jsx)(ro,{})]})]})}function Po(){return(0,_.jsx)("div",{className:"editor-list-view-sidebar",children:(0,_.jsx)(p.__experimentalListView,{})})}function Io(){const{postContentId:e}=(0,l.useSelect)((e=>{const t=e(p.store).getBlocks();return{postContentId:t.find((e=>"core/post-content"===e.name))?.clientId}})),[t]=ut(),{setIsInserterOpened:o}=(0,l.useDispatch)(se.store);return(0,_.jsx)("div",{className:"editor-inserter-sidebar",children:(0,_.jsx)("div",{className:"editor-inserter-sidebar__content",children:(0,_.jsx)(p.__experimentalLibrary,{showMostUsedBlocks:!0,showInserterHelpPanel:!1,rootClientId:"email"===t?e:null,onClose:()=>{o(!1),P("inserter_sidebar_library_close_icon_clicked",{editorMode:t})},onSelect:e=>{P("inserter_sidebar_library_block_selected",{editorMode:t,blockName:e?.name})}})})})}function Mo(){const{isEmailSent:e}=(0,l.useSelect)((e=>({isEmailSent:e(B).isEmailSent()})),[]);return(0,c.useEffect)((()=>{e&&((0,l.dispatch)(no.store).createNotice("warning",(0,g.__)("This email has already been sent. It can be edited, but not sent again. Duplicate this email if you want to send it again.","woocommerce"),{id:"email-sent",isDismissible:!1,context:"email-editor"}),P("editor_showed_email_sent_notice"))}),[e]),null}function Bo(){const{notices:e}=ko();return 0===e.length?null:(0,_.jsx)(f.Notice,{status:"error",className:"woocommerce-email-editor-validation-errors components-editor-notices__pinned",isDismissible:!1,children:(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)("strong",{children:(0,g.__)("Fix errors to continue:","woocommerce")}),(0,_.jsx)("ul",{children:e.map((({id:e,content:t,actions:o})=>(0,_.jsxs)("li",{children:[t,o.length>0?o.map((({label:e,onClick:t})=>(0,_.jsx)(f.Button,{onClick:t,variant:"link",children:e},e))):null]},e)))})]})})}function Ao({context:e="email-editor"}){const{notices:t}=(0,l.useSelect)((t=>({notices:t(no.store).getNotices(e)})),[e]),o=(0,c.useMemo)((()=>({"site-editor-save-success":{content:(0,g.__)("Email design updated.","woocommerce"),removeActions:!0}})),[]),{removeNotice:n}=(0,l.useDispatch)(no.store),s=t.filter((({type:e})=>"snackbar"===e)).map((e=>o[e.id]?{...e,content:o[e.id].content,actions:o[e.id].removeActions?[]:e.actions}:e));return(0,_.jsx)(f.SnackbarList,{notices:s,className:"components-editor-notices__snackbar",onRemove:t=>n(t,e)})}function Fo(){const{notices:e}=(0,l.useSelect)((e=>({notices:e(no.store).getNotices("email-editor")})),[]),{removeNotice:t}=(0,l.useDispatch)(no.store),o=e.filter((({isDismissible:e,type:t})=>e&&"default"===t)),n=e.filter((({isDismissible:e,type:t})=>!e&&"default"===t));return(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(f.NoticeList,{notices:n,className:"components-editor-notices__pinned"}),(0,_.jsx)(f.NoticeList,{notices:o,className:"components-editor-notices__dismissible",onRemove:e=>t(e,"email-editor")}),(0,_.jsx)(Bo,{}),(0,_.jsx)(Ao,{context:"global"}),(0,_.jsx)(Ao,{context:"email-editor"})]})}const Ro=(0,_.jsx)(W.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,_.jsx)(W.Path,{d:"M12 4c-4.4 0-8 3.6-8 8v.1c0 4.1 3.2 7.5 7.2 7.9h.8c4.4 0 8-3.6 8-8s-3.6-8-8-8zm0 15V5c3.9 0 7 3.1 7 7s-3.1 7-7 7z"})}),Lo=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M6.9 7L3 17.8h1.7l1-2.8h4.1l1 2.8h1.7L8.6 7H6.9zm-.7 6.6l1.5-4.3 1.5 4.3h-3zM21.6 17c-.1.1-.2.2-.3.2-.1.1-.2.1-.4.1s-.3-.1-.4-.2c-.1-.1-.1-.3-.1-.6V12c0-.5 0-1-.1-1.4-.1-.4-.3-.7-.5-1-.2-.2-.5-.4-.9-.5-.4 0-.8-.1-1.3-.1s-1 .1-1.4.2c-.4.1-.7.3-1 .4-.2.2-.4.3-.6.5-.1.2-.2.4-.2.7 0 .3.1.5.2.8.2.2.4.3.8.3.3 0 .6-.1.8-.3.2-.2.3-.4.3-.7 0-.3-.1-.5-.2-.7-.2-.2-.4-.3-.6-.4.2-.2.4-.3.7-.4.3-.1.6-.1.8-.1.3 0 .6 0 .8.1.2.1.4.3.5.5.1.2.2.5.2.9v1.1c0 .3-.1.5-.3.6-.2.2-.5.3-.9.4-.3.1-.7.3-1.1.4-.4.1-.8.3-1.1.5-.3.2-.6.4-.8.7-.2.3-.3.7-.3 1.2 0 .6.2 1.1.5 1.4.3.4.9.5 1.6.5.5 0 1-.1 1.4-.3.4-.2.8-.6 1.1-1.1 0 .4.1.7.3 1 .2.3.6.4 1.2.4.4 0 .7-.1.9-.2.2-.1.5-.3.7-.4h-.3zm-3-.9c-.2.4-.5.7-.8.8-.3.2-.6.2-.8.2-.4 0-.6-.1-.9-.3-.2-.2-.3-.6-.3-1.1 0-.5.1-.9.3-1.2s.5-.5.8-.7c.3-.2.7-.3 1-.5.3-.1.6-.3.7-.6v3.4z"})}),zo=(0,_.jsx)(W.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,_.jsx)(W.Path,{d:"M17.2 10.9c-.5-1-1.2-2.1-2.1-3.2-.6-.9-1.3-1.7-2.1-2.6L12 4l-1 1.1c-.6.9-1.3 1.7-2 2.6-.8 1.2-1.5 2.3-2 3.2-.6 1.2-1 2.2-1 3 0 3.4 2.7 6.1 6.1 6.1s6.1-2.7 6.1-6.1c0-.8-.3-1.8-1-3zm-5.1 7.6c-2.5 0-4.6-2.1-4.6-4.6 0-.3.1-1 .8-2.3.5-.9 1.1-1.9 2-3.1.7-.9 1.3-1.7 1.8-2.3.7.8 1.3 1.6 1.8 2.3.8 1.1 1.5 2.2 2 3.1.7 1.3.8 2 .8 2.3 0 2.5-2.1 4.6-4.6 4.6z"})}),Do={start:{scale:1,opacity:1},hover:{scale:0,opacity:0}},Oo={hover:{opacity:1},start:{opacity:.5}},Vo={hover:{scale:1,opacity:1},start:{scale:0,opacity:0}};function Ho({label:e,isFocused:t,withHoverView:o}){const{styles:n,colors:s}=(0,l.useSelect)((e=>({styles:e(B).getStyles(),colors:e(B).getPaletteColors()})),[]),r=n?.color?.background||"#ffffff",i=n?.elements?.heading?.typography?.fontFamily||"inherit",a=n?.elements?.heading?.color?.text||"inherit",d=n?.elements?.heading?.typography?.fontWeight||"inherit",m=s.theme.concat(s.theme),p=m.filter((({color:e})=>e.toLowerCase()!==r.toLowerCase()&&e.toLowerCase()!==a.toLowerCase())).slice(0,2),[u,h]=(0,c.useState)(!1);return(0,_.jsx)("div",{onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),children:(0,_.jsxs)(f.__unstableMotion.div,{style:{height:152,width:"100%",background:r,cursor:o?"pointer":void 0},initial:"start",animate:(u||t)&&e?"hover":"start",children:[(0,_.jsx)(f.__unstableMotion.div,{variants:Do,style:{height:"100%",overflow:"hidden"},children:(0,_.jsxs)(f.__experimentalHStack,{spacing:10,justify:"center",style:{height:"100%",overflow:"hidden"},children:[(0,_.jsx)(f.__unstableMotion.div,{style:{fontFamily:i,fontSize:65,color:a,fontWeight:d},animate:{scale:1,opacity:1},initial:{scale:.1,opacity:0},transition:{delay:.3,type:"tween"},children:"Aa"}),(0,_.jsx)(f.__experimentalVStack,{spacing:4,children:p.map((({slug:e,color:t},o)=>(0,_.jsx)(f.__unstableMotion.div,{style:{height:32,width:32,background:t,borderRadius:16},animate:{scale:1,opacity:1},initial:{scale:.1,opacity:0},transition:{delay:1===o?.2:.1}},e)))})]})}),(0,_.jsx)(f.__unstableMotion.div,{variants:o&&Oo,style:{height:"100%",width:"100%",position:"absolute",top:0,overflow:"hidden",filter:"blur(60px)",opacity:.1},children:(0,_.jsx)(f.__experimentalHStack,{spacing:0,justify:"flex-start",style:{height:"100%",overflow:"hidden"},children:m.slice(0,4).map((({color:e})=>(0,_.jsx)("div",{style:{height:"100%",background:e,flexGrow:1}},e)))})}),(0,_.jsx)(f.__unstableMotion.div,{variants:Vo,style:{height:"100%",width:"100%",overflow:"hidden",position:"absolute",top:0},children:(0,_.jsx)(f.__experimentalVStack,{spacing:3,justify:"center",style:{height:"100%",overflow:"hidden",padding:10,boxSizing:"border-box"},children:e&&(0,_.jsx)("div",{style:{fontSize:40,fontFamily:i,color:a,fontWeight:d,lineHeight:"1em",textAlign:"center"},children:e})})})]})})}function Go(){return(0,_.jsx)(f.Card,{size:"small",className:"edit-site-global-styles-screen-root",variant:"primary",children:(0,_.jsx)(f.CardBody,{children:(0,_.jsxs)(f.__experimentalVStack,{spacing:4,children:[(0,_.jsx)(f.Card,{children:(0,_.jsx)(f.CardMedia,{children:(0,_.jsx)(Ho,{})})}),(0,_.jsxs)(f.__experimentalItemGroup,{children:[(0,_.jsx)(f.__experimentalNavigatorButton,{path:"/typography",onClick:()=>P("styles_sidebar_navigation_click",{path:"typography"}),children:(0,_.jsx)(f.__experimentalItem,{children:(0,_.jsxs)(f.__experimentalHStack,{justify:"flex-start",children:[(0,_.jsx)(f.Icon,{icon:Lo,size:24}),(0,_.jsx)(f.FlexItem,{children:(0,g.__)("Typography","woocommerce")})]})})}),(0,_.jsx)(f.__experimentalNavigatorButton,{path:"/colors",onClick:()=>P("styles_sidebar_navigation_click",{path:"colors"}),children:(0,_.jsx)(f.__experimentalItem,{children:(0,_.jsxs)(f.__experimentalHStack,{justify:"flex-start",children:[(0,_.jsx)(f.Icon,{icon:zo,size:24}),(0,_.jsx)(f.FlexItem,{children:(0,g.__)("Colors","woocommerce")})]})})}),(0,_.jsx)(f.__experimentalNavigatorButton,{path:"/layout",onClick:()=>P("styles_sidebar_navigation_click",{path:"layout"}),children:(0,_.jsx)(f.__experimentalItem,{children:(0,_.jsxs)(f.__experimentalHStack,{justify:"flex-start",children:[(0,_.jsx)(f.Icon,{icon:Ot,size:24}),(0,_.jsx)(f.FlexItem,{children:(0,g.__)("Layout","woocommerce")})]})})})]})]})})})}function $o(e){const t=e=>{if("object"==typeof e&&null!==e||void 0===e){if(Array.isArray(e)&&0===e.length)return;for(const o in e)if(e.hasOwnProperty(o)){const n=t(e[o]);void 0===n?delete e[o]:e[o]=n}}return e};return t(e)}const Uo=()=>{const{userTheme:e,updateUserTheme:t}=nt(),o=(0,c.useMemo)((()=>$o(function(e){const t=e=>{if("object"==typeof e&&null!==e)for(const o in e)e.hasOwnProperty(o)&&(e[o]=t(e[o]));else if("string"==typeof e)return e.replace(/var\(--([a-z]+)--([a-z]+(?:--[a-z0-9]+(?:-[a-z0-9]+)*)*)--([a-z0-9-]+)\)/g,((e,t,o,n)=>`var:${o.split("--").concat(n).join("|")}`));return e};return t(e)}(e?.styles))),[e]),{styles:n}=(0,l.useSelect)((e=>({styles:e(B).getStyles()}))),s=(0,c.useCallback)((o=>{const n={...e,styles:$o(o)};t(n)}),[t,e]),r=(0,c.useCallback)(((o,n)=>{const s=function(e,t,o){const n=Array.isArray(t)?[...t]:[t],s=Array.isArray(e)?[...e]:{...e},r=n.pop();let i=s;return n.forEach((e=>{const t=i[e];i[e]=Array.isArray(t)?[...t]:{...t},i=i[e]})),i[r]=o,s}(e,["styles",...o],n);t(s)}),[t,e]);return{styles:ot().all([n||{},o||{}]),userStyles:e?.styles,defaultStyles:n,updateStyleProp:r,updateStyles:s}},Wo={typography:{},color:{}},qo=(e,t,o="heading",n=!1)=>{switch(t){case"text":return{typography:e.typography,color:e.color};case"heading":return((e,t="heading",o=!1)=>o?ot().all([Wo,e.elements.heading||{},e.elements[t]||{}]):{...Wo,...e.elements.heading||{},...e.elements[t]||{}})(e,null!=o?o:"heading",n);default:return e.elements[t]||Wo}};function Zo({element:e,label:t}){const{styles:o}=Uo(),n=qo(o,e,null,!0),{fontFamily:s,fontStyle:r,fontWeight:i,letterSpacing:a,textDecoration:l,textTransform:c}=n.typography,d=n.color?.text||"inherit",m=n.color?.background||"#f0f0f0",p=(0,g.sprintf)((0,g.__)("Typography %s styles","woocommerce"),t);return(0,_.jsx)(f.__experimentalItem,{children:(0,_.jsx)(f.__experimentalNavigatorButton,{path:`/typography/${e}`,"aria-label":p,onClick:()=>P("styles_sidebar_screen_typography_button_click",{element:e,label:t,path:`typography/${e}`}),children:(0,_.jsxs)(f.__experimentalHStack,{justify:"flex-start",children:[(0,_.jsx)(f.FlexItem,{className:"edit-site-global-styles-screen-typography__indicator",style:{fontFamily:null!=s?s:"serif",background:m,color:d,fontStyle:null!=r?r:"normal",fontWeight:null!=i?i:"normal",letterSpacing:null!=a?a:"normal",textDecoration:null!=l?l:"link"===e?"underline":"none",textTransform:null!=c?c:"none"},children:"Aa"}),(0,_.jsx)(f.FlexItem,{children:t})]})})})}const Yo=function(){return(0,_.jsx)(f.Card,{size:"small",variant:"primary",isBorderless:!0,children:(0,_.jsx)(f.CardBody,{children:(0,_.jsxs)(f.__experimentalVStack,{spacing:3,children:[(0,_.jsx)(f.__experimentalHeading,{level:3,className:"edit-site-global-styles-subtitle",children:(0,g.__)("Elements","woocommerce")}),(0,_.jsxs)(f.__experimentalItemGroup,{isBordered:!0,isSeparated:!0,size:"small",children:[(0,_.jsx)(Zo,{element:"text",label:(0,g.__)("Text","woocommerce")}),(0,_.jsx)(Zo,{element:"link",label:(0,g.__)("Links","woocommerce")}),(0,_.jsx)(Zo,{element:"heading",label:(0,g.__)("Headings","woocommerce")}),(0,_.jsx)(Zo,{element:"button",label:(0,g.__)("Buttons","woocommerce")})]})]})})})},Jo=(0,_.jsx)(W.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,_.jsx)(W.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})});function Xo({title:e,description:t,onBack:o}){return(0,_.jsxs)(f.__experimentalVStack,{spacing:0,children:[(0,_.jsx)(f.__experimentalView,{children:(0,_.jsx)(f.__experimentalSpacer,{marginBottom:0,paddingX:4,paddingY:3,children:(0,_.jsxs)(f.__experimentalHStack,{spacing:2,children:[(0,_.jsx)(f.__experimentalNavigatorToParentButton,{style:{minWidth:24,padding:0},icon:Jo,size:"small","aria-label":(0,g.__)("Navigate to the previous view","woocommerce"),onClick:o}),(0,_.jsx)(f.__experimentalSpacer,{children:(0,_.jsx)(f.__experimentalHeading,{className:"woocommerce-email-editor-styles-header",level:2,size:13,children:e})})]})})}),t&&(0,_.jsx)("p",{className:"woocommerce-email-editor-styles-header-description",children:t})]})}const Ko=Xo;function Qo(){return I("styles_sidebar_screen_typography_opened"),(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(Ko,{title:(0,g.__)("Typography","woocommerce"),description:(0,g.__)("Manage the typography settings for different elements.","woocommerce")}),(0,_.jsx)(Yo,{})]})}const en={fontFamily:!0,fontSize:!0,fontAppearance:!0,lineHeight:!0,letterSpacing:!1,textTransform:!1,textDecoration:!1,writingMode:!0,textColumns:!0},tn=function({element:e,headingLevel:t,defaultControls:o=en}){const[n,s]=(0,p.useSettings)("typography.fontSizes","typography.fontFamilies"),r=s?.default||[],{styles:i,defaultStyles:a,updateStyleProp:l}=Uo(),d=qo(i,e,t),m=qo(a,e,t),{fontFamily:u,fontSize:h,fontStyle:x,fontWeight:y,lineHeight:w,letterSpacing:v,textDecoration:b,textTransform:j}=d.typography,{fontFamily:S,fontSize:k,fontStyle:C,fontWeight:T,lineHeight:E,letterSpacing:N,textDecoration:I,textTransform:B}=m.typography,A="heading"!==e||"heading"!==t,F=(0,c.useCallback)(((o,n)=>{l("heading"===e?["elements",t,...o]:"text"===e?[...o]:["elements",e,...o],n)}),[e,l,t]),R=t=>{F(["typography","letterSpacing"],t),M("styles_sidebar_screen_typography_element_panel_set_letter_spacing",{element:e,newValue:t,selectedDefaultLetterSpacing:t===N})},L=t=>{F(["typography","lineHeight"],t),M("styles_sidebar_screen_typography_element_panel_set_line_height",{element:e,newValue:t,selectedDefaultLineHeight:t===E})},z=o=>{F(["typography","fontSize"],o),M("styles_sidebar_screen_typography_element_panel_set_font_size",{element:e,headingLevel:t,newValue:o,selectedDefaultFontSize:o===k})},D=t=>{F(["typography","fontFamily"],t),M("styles_sidebar_screen_typography_element_panel_set_font_family",{element:e,newValue:t,selectedDefaultFontFamily:t===S})},O=t=>{F(["typography","textDecoration"],t),M("styles_sidebar_screen_typography_element_panel_set_text_decoration",{element:e,newValue:t,selectedDefaultTextDecoration:t===I})},V=t=>{F(["typography","textTransform"],t),M("styles_sidebar_screen_typography_element_panel_set_text_transform",{element:e,newValue:t,selectedDefaultTextTransform:t===B})},H=({fontStyle:t,fontWeight:o})=>{F(["typography","fontStyle"],t),F(["typography","fontWeight"],o),M("styles_sidebar_screen_typography_element_panel_set_font_appearance",{element:e,newFontStyle:t,newFontWeight:o,selectedDefaultFontStyle:t===C,selectedDefaultFontWeight:o===T})};return(0,_.jsxs)(f.__experimentalToolsPanel,{label:(0,g.__)("Typography","woocommerce"),resetAll:()=>{F(["typography"],m.typography),P("styles_sidebar_screen_typography_element_panel_reset_all_styles_selected",{element:e,headingLevel:t})},children:[(0,_.jsx)(f.__experimentalToolsPanelItem,{label:(0,g.__)("Font family","woocommerce"),hasValue:()=>u!==S,onDeselect:()=>D(S),isShownByDefault:o.fontFamily,children:(0,_.jsx)(p.__experimentalFontFamilyControl,{value:u,onChange:D,size:"__unstable-large",fontFamilies:r,__nextHasNoMarginBottom:!0})}),A&&(0,_.jsx)(f.__experimentalToolsPanelItem,{label:(0,g.__)("Font size","woocommerce"),hasValue:()=>h!==k,onDeselect:()=>z(k),isShownByDefault:o.fontSize,children:(0,_.jsx)(f.FontSizePicker,{value:h,onChange:z,fontSizes:n,disableCustomFontSizes:!1,withReset:!1,withSlider:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0})}),(0,_.jsx)(f.__experimentalToolsPanelItem,{className:"single-column",label:(0,g.__)("Appearance","woocommerce"),hasValue:()=>y!==T||x!==C,onDeselect:()=>{H({fontStyle:C,fontWeight:T})},isShownByDefault:o.fontAppearance,children:(0,_.jsx)(p.__experimentalFontAppearanceControl,{value:{fontStyle:x,fontWeight:y},onChange:H,hasFontStyles:!0,hasFontWeights:!0,size:"__unstable-large"})}),(0,_.jsx)(f.__experimentalToolsPanelItem,{className:"single-column",label:(0,g.__)("Line height","woocommerce"),hasValue:()=>w!==E,onDeselect:()=>L(E),isShownByDefault:o.lineHeight,children:(0,_.jsx)(p.LineHeightControl,{__nextHasNoMarginBottom:!0,__unstableInputWidth:"auto",value:w,onChange:L,size:"__unstable-large"})}),(0,_.jsx)(f.__experimentalToolsPanelItem,{className:"single-column",label:(0,g.__)("Letter spacing","woocommerce"),hasValue:()=>v!==N,onDeselect:()=>R(N),isShownByDefault:o.letterSpacing,children:(0,_.jsx)(p.__experimentalLetterSpacingControl,{value:v,onChange:R,size:"__unstable-large",__unstableInputWidth:"auto"})}),(0,_.jsx)(f.__experimentalToolsPanelItem,{className:"single-column",label:(0,g.__)("Text decoration","woocommerce"),hasValue:()=>b!==I,onDeselect:()=>O(I),isShownByDefault:o.textDecoration,children:(0,_.jsx)(p.__experimentalTextDecorationControl,{value:b,onChange:O,size:"__unstable-large",__unstableInputWidth:"auto"})}),(0,_.jsx)(f.__experimentalToolsPanelItem,{label:(0,g.__)("Letter case","woocommerce"),hasValue:()=>j!==B,onDeselect:()=>V(B),isShownByDefault:o.textTransform,children:(0,_.jsx)(p.__experimentalTextTransformControl,{value:j,onChange:V,showNone:!0,isBlock:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0})})]})};function on({element:e,headingLevel:t}){const{styles:o}=Uo(),n=qo(o,e,t,!0),{fontFamily:s,fontSize:r,fontStyle:i,fontWeight:a,lineHeight:l,letterSpacing:c,textDecoration:d,textTransform:m}=n.typography,p=n.color?.text||"inherit",u=n.color?.background||"#f0f0f0",h="link"===e?{textDecoration:null!=d?d:"underline"}:{};return(0,_.jsx)("div",{className:"edit-site-typography-preview",style:{fontFamily:null!=s?s:"serif",background:u,color:p,lineHeight:l,fontSize:r,fontStyle:i,fontWeight:a,letterSpacing:c,textDecoration:d,textTransform:m,...h},children:"Aa"})}const nn={text:{title:(0,g.__)("Text","woocommerce"),description:(0,g.__)("Manage the fonts and typography used on text.","woocommerce"),defaultControls:en},link:{title:(0,g.__)("Links","woocommerce"),description:(0,g.__)("Manage the fonts and typography used on links.","woocommerce"),defaultControls:{...en,textDecoration:!0}},heading:{title:(0,g.__)("Headings","woocommerce"),description:(0,g.__)("Manage the fonts and typography used on headings.","woocommerce"),defaultControls:{...en,textTransform:!0}},button:{title:(0,g.__)("Buttons","woocommerce"),description:(0,g.__)("Manage the fonts and typography used on buttons.","woocommerce"),defaultControls:en}};function sn({element:e}){I("styles_sidebar_screen_typography_element_opened",{element:e});const[t,o]=(0,c.useState)("heading");return(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(Ko,{title:nn[e].title,description:nn[e].description}),(0,_.jsx)(f.__experimentalSpacer,{marginX:4,children:(0,_.jsx)(on,{element:e,headingLevel:t})}),"heading"===e&&(0,_.jsx)(f.__experimentalSpacer,{marginX:4,marginBottom:"1em",children:(0,_.jsxs)(f.__experimentalToggleGroupControl,{label:(0,g.__)("Select heading level","woocommerce"),hideLabelFromVision:!0,value:t,onChange:e=>{o(e.toString()),P("styles_sidebar_screen_typography_element_heading_level_selected",{value:e})},isBlock:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0,children:[(0,_.jsx)(f.__experimentalToggleGroupControlOption,{value:"heading",label:(0,g._x)("All","heading levels","woocommerce")}),(0,_.jsx)(f.__experimentalToggleGroupControlOption,{value:"h1",label:(0,g._x)("H1","Heading Level","woocommerce")}),(0,_.jsx)(f.__experimentalToggleGroupControlOption,{value:"h2",label:(0,g._x)("H2","Heading Level","woocommerce")}),(0,_.jsx)(f.__experimentalToggleGroupControlOption,{value:"h3",label:(0,g._x)("H3","Heading Level","woocommerce")}),(0,_.jsx)(f.__experimentalToggleGroupControlOption,{value:"h4",label:(0,g._x)("H4","Heading Level","woocommerce")}),(0,_.jsx)(f.__experimentalToggleGroupControlOption,{value:"h5",label:(0,g._x)("H5","Heading Level","woocommerce")}),(0,_.jsx)(f.__experimentalToggleGroupControlOption,{value:"h6",label:(0,g._x)("H6","Heading Level","woocommerce")})]})}),(0,_.jsx)(tn,{element:e,headingLevel:t,defaultControls:nn[e].defaultControls})]})}function rn(){I("styles_sidebar_screen_colors_opened");const{userStyles:e,styles:t,updateStyles:o}=Uo(),n=(0,l.useSelect)((e=>e(B).getTheme()),[]);return(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(Ko,{title:(0,g.__)("Colors","woocommerce"),description:(0,g.__)("Manage palettes and the default color of different global elements.","woocommerce")}),(0,_.jsx)(lt,{value:e,inheritedValue:t,onChange:e=>{o(e),P("styles_sidebar_screen_colors_styles_updated")},settings:n?.settings,panelId:"colors"})]})}function an(){const[e]=(0,p.useSettings)("spacing.units"),t=(0,f.__experimentalUseCustomUnits)({availableUnits:e}),{styles:o,defaultStyles:n,updateStyleProp:s}=Uo();return(0,_.jsxs)(f.__experimentalToolsPanel,{label:(0,g.__)("Dimensions","woocommerce"),resetAll:()=>{s(["spacing"],n.spacing),P("styles_sidebar_screen_layout_dimensions_reset_all_selected")},children:[(0,_.jsx)(f.__experimentalToolsPanelItem,{isShownByDefault:!0,hasValue:()=>!(0,C.isEqual)(o.spacing.padding,n.spacing.padding),label:(0,g.__)("Padding","woocommerce"),onDeselect:()=>{s(["spacing","padding"],n.spacing.padding),P("styles_sidebar_screen_layout_dimensions_padding_reset_clicked")},className:"tools-panel-item-spacing",children:(0,_.jsx)(p.__experimentalSpacingSizesControl,{allowReset:!0,values:o.spacing.padding,onChange:e=>{s(["spacing","padding"],e),M("styles_sidebar_screen_layout_dimensions_padding_updated",{value:e})},label:(0,g.__)("Padding","woocommerce"),sides:["horizontal","vertical","top","left","right","bottom"],units:t})}),(0,_.jsx)(f.__experimentalToolsPanelItem,{isShownByDefault:!0,label:(0,g.__)("Block spacing","woocommerce"),hasValue:()=>o.spacing.blockGap!==n.spacing.blockGap,onDeselect:()=>{s(["spacing","blockGap"],n.spacing.blockGap),P("styles_sidebar_screen_layout_dimensions_block_spacing_reset_clicked")},className:"tools-panel-item-spacing",children:(0,_.jsx)(p.__experimentalSpacingSizesControl,{label:(0,g.__)("Block spacing","woocommerce"),min:0,onChange:e=>{s(["spacing","blockGap"],e.top),M("styles_sidebar_screen_layout_dimensions_block_spacing_updated",{value:e})},showSideInLabel:!1,sides:["top"],values:{top:o.spacing.blockGap},allowReset:!0})})]})}function ln(){return I("styles_sidebar_screen_layout_opened"),(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(Xo,{title:(0,g.__)("Layout","woocommerce")}),(0,_.jsx)(an,{})]})}const cn=(0,c.memo)((function(e){const{userCanEditGlobalStyles:t}=(0,l.useSelect)((e=>{const{canEdit:t}=e(B).canUserEditGlobalEmailStyles();return{userCanEditGlobalStyles:t}}),[]);return t&&(0,_.jsx)(Ye,{identifier:"email-editor/editor/styles",className:"woocommerce-email-editor-styles-panel",header:(0,g.__)("Styles","woocommerce"),closeLabel:(0,g.__)("Close styles sidebar","woocommerce"),icon:Ro,scope:B,...e,children:(0,_.jsxs)(f.__experimentalNavigatorProvider,{initialPath:"/",children:[(0,_.jsx)(f.__experimentalNavigatorScreen,{path:"/",children:(0,_.jsx)(Go,{})}),(0,_.jsx)(f.__experimentalNavigatorScreen,{path:"/typography",children:(0,_.jsx)(Qo,{})}),(0,_.jsx)(f.__experimentalNavigatorScreen,{path:"/typography/text",children:(0,_.jsx)(sn,{element:"text"})}),(0,_.jsx)(f.__experimentalNavigatorScreen,{path:"/typography/link",children:(0,_.jsx)(sn,{element:"link"})}),(0,_.jsx)(f.__experimentalNavigatorScreen,{path:"/typography/heading",children:(0,_.jsx)(sn,{element:"heading"})}),(0,_.jsx)(f.__experimentalNavigatorScreen,{path:"/typography/button",children:(0,_.jsx)(sn,{element:"button"})}),(0,_.jsx)(f.__experimentalNavigatorScreen,{path:"/colors",children:(0,_.jsx)(rn,{})}),(0,_.jsx)(f.__experimentalNavigatorScreen,{path:"/layout",children:(0,_.jsx)(ln,{})})]})})}));function dn({contentRef:e}){const{onNavigateToEntityRecord:t,templateId:o,canUpdateTemplates:n}=(0,l.useSelect)((e=>{const{getEditorSettings:t,getCurrentTemplateId:o}=e(se.store);return{onNavigateToEntityRecord:t().onNavigateToEntityRecord,templateId:o(),canUpdateTemplates:e(B).canUserEditTemplates()}}),[]),[s,r]=(0,c.useState)("");return(0,c.useEffect)((()=>{const t=e=>{e.target.classList.contains("is-root-container")&&(r(n?"confirm":"info"),I("edit_template_blocks_notification_opened"))},o=e.current;return o?.addEventListener("dblclick",t),()=>{o?.removeEventListener("dblclick",t)}}),[e,n]),(0,_.jsxs)(_.Fragment,{children:["info"===s&&(0,_.jsxs)(f.Modal,{onRequestClose:()=>{r(""),P("edit_template_blocks_notification_none_admin_role_notice_closed")},__experimentalHideHeader:!0,children:[(0,_.jsx)("p",{children:(0,g.__)("You are attempting to edit a part of the template. Only site admins can edit email templates.","woocommerce")}),(0,_.jsx)("div",{className:"woocommerce-send-preview-modal-footer",children:(0,_.jsx)(f.Button,{variant:"tertiary",onClick:()=>{r(""),P("edit_template_blocks_notification_none_admin_role_notice_closed")},children:(0,g.__)("Cancel","woocommerce")})})]}),(0,_.jsx)(f.__experimentalConfirmDialog,{isOpen:"confirm"===s,confirmButtonText:(0,g.__)("Edit template","woocommerce"),onConfirm:()=>{r(""),t({postId:o,postType:"wp_template"}),P("edit_template_blocks_notification_edit_template_button_clicked",{templateId:o})},onCancel:()=>{r(""),P("edit_template_blocks_notification_cancel_button_clicked",{templateId:o})},size:"medium",children:(0,g.__)("The block you’ve selected is part of a template that might be used in other emails. Are you sure you want to edit the template?","woocommerce")})]})}function mn(e,t,o){return Math.min(Math.max(e,t),o)}function pn({isEnabled:e=!0}={}){const t=(e=>{const{getEnabledClientIdsTree:t}=rt(e);return t})((0,l.useSelect)(p.store)),{getBlockName:o,getBlockOrder:n}=(0,l.useSelect)(p.store),{selectBlock:s}=(0,l.useDispatch)(p.store);return(0,u.useRefEffect)((r=>{if(!e)return null;const i=e=>{(e.target===r||e.target.classList.contains("is-root-container"))&&((e,i)=>{const a=t().flatMap((({clientId:e})=>{const t=o(e);if("core/template-part"===t)return[];if("core/post-content"===t){const t=n(e);if(t.length)return t}return[e]})).reduce(((t,o)=>{const n=r.querySelector(`[data-block="${o}"]`);if(!n)return t;const s=n.getBoundingClientRect(),a=function(e,t,o){const n=e-mn(e,Number(o.left),Number(o.right)),s=t-mn(t,Number(o.top),Number(o.bottom));return Math.sqrt(n*n+s*s)}(Number(e),Number(i),s);return a<t.distance&&a<500?{clientId:o,distance:a}:t}),{clientId:null,distance:Number.POSITIVE_INFINITY}),l=a?.clientId||"";l&&s(l)})(e.clientX,e.clientY)};return r.addEventListener("click",i),()=>r.removeEventListener("click",i)}),[e])}const un="wp_block",_n=[un,"wp_template","wp_navigation","wp_template_part"];function hn({styles:e,disableIframe:t=!1,iframeProps:o,contentRef:n,className:s}){var r;const{renderingMode:i,wrapperBlockName:a,wrapperUniqueId:d,deviceType:m,isFocusedEntity:h,layout:g}=(0,l.useSelect)((e=>{const{getCurrentPostId:t,getCurrentPostType:o,getEditorSettings:n,getRenderingMode:s,getDeviceType:r}=e(se.store),i=o(),a=s();let l;i===un?l="core/block":"post-only"===a&&(l="core/post-content");const c=n();return{renderingMode:a,isDesignPostType:_n.includes(i),wrapperBlockName:l,wrapperUniqueId:t(),deviceType:r(),isFocusedEntity:!!c.onNavigateToPreviousEntityRecord,postType:i,isPreview:c.__unstableIsPreviewMode,layout:c.__experimentalFeatures.layout}}),[]),x=(0,p.__experimentalUseResizeCanvas)(m),y=g,w=(0,c.useRef)(),f=(0,p.__unstableUseTypewriter)(),v=(0,u.useMergeRefs)([w,n,"post-only"===i?f:null,pn({isEnabled:"template-locked"===i})]),b=!t||["Tablet","Mobile"].includes(m),j=[...null!==(r=e)&&void 0!==r?r:[],{css:`.is-root-container{display:flow-root; width:${"Desktop"===m?g.contentSize:"100%"}; margin: 0 auto;box-sizing: border-box;}`}];return(0,_.jsx)("div",{className:$()("editor-visual-editor","edit-post-visual-editor",s,{"has-padding":h,"is-iframed":b}),children:(0,_.jsx)(it,{shouldIframe:b,contentRef:v,styles:j,height:"100%",iframeProps:{...o,style:{...o?.style,...x}},children:(0,_.jsxs)(p.RecursionProvider,{blockName:a,uniqueId:d,children:[(0,_.jsx)(p.BlockList,{className:$()(`is-${m.toLowerCase()}-preview`,"has-global-padding"),layout:y,dropZoneElement:t?w.current:w.current?.parentNode,__unstableDisableDropZone:"template-locked"===i}),"template-locked"===i&&(0,_.jsx)(dn,{contentRef:w})]})})})}function gn(){const[e,t]=(0,c.useState)(!1),{emailContentIsEmpty:o,emailHasEdits:n}=(0,l.useSelect)((e=>({emailContentIsEmpty:e(B).hasEmptyContent(),emailHasEdits:e(B).hasEdits()})),[]);return!o||n||e?null:(0,_.jsx)(It,{onSelectCallback:()=>t(!0)})}function xn(){I("editor_layout_loaded");const{isFullscreenActive:e,isSidebarOpened:t,initialSettings:o,previewDeviceType:n,isInserterSidebarOpened:s,isListviewSidebarOpened:r,canUserEditMedia:i,hasFixedToolbar:a,focusMode:d,styles:m}=(0,l.useSelect)((e=>({isFullscreenActive:e(B).isFeatureActive("fullscreenMode"),isSidebarOpened:e(B).isSidebarOpened(),isInserterSidebarOpened:e(se.store).isInserterOpened(),isListviewSidebarOpened:e(se.store).isListViewOpened(),initialSettings:e(B).getInitialEditorSettings(),previewDeviceType:e(B).getDeviceType(),canUserEditMedia:e(re.store).canUser("create","media"),hasFixedToolbar:e(B).isFeatureActive("fixedToolbar"),focusMode:e(B).isFeatureActive("focusMode"),styles:e(B).getStyles()})),[]),{setIsInserterOpened:u}=(0,l.useDispatch)(se.store),[h]=dt(),g=$()("edit-post-layout",{"is-sidebar-opened":t}),x=(0,c.useRef)(null),y={background:"Desktop"===n?m.color.background:"transparent",fontFamily:m.typography.fontFamily,transition:"all 0.3s ease 0s"},w={...o,mediaUpload:i?ie.uploadMedia:null,hasFixedToolbar:a,focusMode:d};return(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(Je,{isActive:e}),(0,_.jsx)(se.UnsavedChangesWarning,{}),(0,_.jsx)(mt,{}),(0,_.jsx)(Mo,{}),(0,_.jsx)(qt,{}),(0,_.jsx)(cn,{}),(0,_.jsx)(gn,{}),(0,_.jsx)(et,{className:g,header:(0,_.jsx)(No,{}),editorNotices:(0,_.jsx)(Fo,{}),content:(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(Fo,{}),(0,_.jsx)(p.BlockSelectionClearer,{className:"edit-post-visual-editor",style:y,onClick:()=>{s&&u(!1)},children:(0,_.jsx)("div",{className:"visual-editor__email_content_wrapper",children:(0,_.jsx)(hn,{disableIframe:!1,styles:[...w.styles,...h],className:"has-global-padding",contentRef:x,iframeProps:{}})})})]}),sidebar:(0,_.jsx)(Ye.Slot,{scope:B}),secondarySidebar:s&&(0,_.jsx)(Io,{})||r&&(0,_.jsx)(Po,{})}),(0,_.jsx)(Yt,{})]})}function yn({postId:e,postType:t,settings:o,initialEdits:n,...s}){const{currentPost:r,onNavigateToEntityRecord:i,onNavigateToPreviousEntityRecord:a}=function(e,t,o){const[n,s]=(0,c.useReducer)(((e,{type:t,post:o,previousRenderingMode:n})=>"push"===t?[...e,{post:o,previousRenderingMode:n}]:"pop"===t&&e.length>1?e.slice(0,-1):e),[{post:{postId:e,postType:t}}]),{post:r,previousRenderingMode:i}=n[n.length-1],{getRenderingMode:a}=(0,l.useSelect)(se.store),{setRenderingMode:d}=(0,l.useDispatch)(se.store),m=(0,c.useCallback)((e=>{s({type:"push",post:{postId:e.postId,postType:e.postType},previousRenderingMode:a()}),d(o)}),[a,d,o]),p=(0,c.useCallback)((()=>{s({type:"pop"}),i&&d(i)}),[d,i]);return{currentPost:r,onNavigateToEntityRecord:m,onNavigateToPreviousEntityRecord:n.length>1?p:void 0}}(e,t,"post-only"),{post:d,template:m}=(0,l.useSelect)((e=>{const{getEntityRecord:t}=e(re.store),{getEditedPostTemplate:o}=e(B),n=t("postType",r.postType,r.postId);return{template:"wp_template"!==r.postType?o():null,post:n}}),[r.postType,r.postId]),p=(0,l.useSelect)((e=>{const{hasFinishedResolution:t,getBlockPatternsForPostType:o}=(e=>{const{hasFinishedResolution:t,getBlockPatternsForPostType:o}=rt(e(re.store));return{hasFinishedResolution:t,getBlockPatternsForPostType:o}})(e),n=o(r.postType);return t("getBlockPatterns")?n:void 0}),[r.postType]),u=(0,c.useMemo)((()=>({...o,onNavigateToEntityRecord:i,onNavigateToPreviousEntityRecord:a,defaultRenderingMode:"wp_template"===r.postType?"post-only":"template-locked",supportsTemplateMode:!0,__experimentalBlockPatterns:p})),[o,i,a,p,r.postType]);return!d||"wp_template"!==r.postType&&!m?(0,_.jsx)("div",{className:"spinner-container",children:(0,_.jsx)(f.Spinner,{style:{width:"80px",height:"80px"}})}):(0,_.jsx)(f.SlotFillProvider,{children:(0,_.jsx)(se.EditorProvider,{settings:u,post:d,initialEdits:n,useSubRegistry:!1,__unstableTemplate:m,...s,children:(0,_.jsxs)(se.ErrorBoundary,{children:[(0,_.jsx)(xn,{}),(0,_.jsx)(se.PostLockedModal,{})]})})})}const wn=window.wp.dataControls,fn=window.wp.apiFetch;var vn=o.n(fn);const bn=window.wp.htmlEntities,jn=e=>({registry:t})=>t.dispatch(ue.store).toggle(B,e),Sn=e=>({registry:t})=>{t.dispatch(se.store).setDeviceType(e)};function kn(e){return{type:"CHANGE_PREVIEW_STATE",state:{isModalOpened:e}}}function Cn(e,t={}){return{type:"CHANGE_PERSONALIZATION_TAGS_STATE",state:{isModalOpened:e,...t}}}function Tn(e){return{type:"CHANGE_PREVIEW_STATE",state:{toEmail:e}}}const En=(e=A)=>({registry:t})=>t.dispatch(Fe).enableComplementaryArea(B,e),Nn=()=>({registry:e})=>e.dispatch(Fe).disableComplementaryArea(B);function Pn(e){return{type:"TOGGLE_SETTINGS_SIDEBAR_ACTIVE_TAB",state:{activeTab:e}}}function*In(){const e=(0,l.select)(B).getEmailPostId(),t=yield(0,l.dispatch)(re.store).saveEditedEntityRecord("postType",R,e,{throwOnError:!0});t.then((()=>{(0,l.dispatch)(no.store).createErrorNotice((0,g.__)("Email saved!","woocommerce"),{type:"snackbar",isDismissible:!0,context:"email-editor"})})),t.catch((()=>{(0,l.dispatch)(no.store).createErrorNotice((0,g.__)("The email could not be saved. Please, clear browser cache and reload the page. If the problem persists, duplicate the email and try again.","woocommerce"),{type:"default",isDismissible:!0,context:"email-editor"})}))}const Mn=e=>async({registry:t})=>{const o=t.select(B).getEmailPostId();t.dispatch(re.store).editEntityRecord("postType",R,o,{template:e})};function*Bn(e){if(!(0,l.select)(B).getPreviewState().isSendingPreviewEmail){yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:null,isSendingPreviewEmail:!0}};try{const t=(0,l.select)(B).getEmailPostId();yield(0,wn.apiFetch)({path:"/woocommerce-email-editor/v1/send_preview_email",method:"POST",data:{email:e,postId:t}}),yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:mo.SUCCESS,isSendingPreviewEmail:!1}},P("sent_preview_email",{postId:t,email:e})}catch(t){P("sent_preview_email_error",{email:e}),yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:mo.ERROR,isSendingPreviewEmail:!1,errorMessage:JSON.stringify(t?.error)}}}}}function An(e){return async({registry:t})=>{try{const o=t.select(re.store).getEntityConfig("postType",e.type),n=(0,co.addQueryArgs)(`${o.baseURL}/${e.id}`,{context:"edit",source:"theme"}),s=await vn()({path:n}),r=({blocks:e=[]})=>(0,U.__unstableSerializeAndClean)(e),i=(0,U.parse)(s?.content?.raw);await t.dispatch(re.store).editEntityRecord("postType",e.type,s.id,{content:r,blocks:i,source:"theme"}),await t.dispatch(re.store).saveEditedEntityRecord("postType",e.type,e.id,{}),t.dispatch(no.store).createSuccessNotice((0,g.sprintf)((0,g.__)('"%s" reset.',"woocommerce"),(0,bn.decodeEntities)(e.title)),{type:"snackbar",id:"edit-site-template-reverted"})}catch(e){t.dispatch(no.store).createErrorNotice((0,g.__)("An error occurred while reverting the template.","woocommerce"),{type:"snackbar"})}}}function Fn(e){return{type:"SET_IS_FETCHING_PERSONALIZATION_TAGS",state:{isFetching:e}}}function Rn(e){return{type:"SET_PERSONALIZATION_TAGS_LIST",state:{list:e}}}function Ln(e,t){switch(t.type){case"CHANGE_PREVIEW_STATE":return{...e,preview:{...e.preview,...t.state}};case"CHANGE_PERSONALIZATION_TAGS_STATE":case"SET_IS_FETCHING_PERSONALIZATION_TAGS":case"SET_PERSONALIZATION_TAGS_LIST":return{...e,personalizationTags:{...e.personalizationTags,...t.state}};case"TOGGLE_SETTINGS_SIDEBAR_ACTIVE_TAB":return{...e,settingsSidebar:{...e.settingsSidebar,activeTab:t.state.activeTab}};case"SET_PERSONALIZATION_TAGS":return{...e,personalizationTags:{...e.personalizationTags,list:t.personalizationTags}};default:return e}}function zn(e){return e?.content&&"function"==typeof e.content?e.content(e):e?.blocks?(0,U.serialize)(e.blocks):e?.content?e.content:""}const Dn=new WeakMap;function On(e){let t=Dn.get(e);return t||(t={...e,get blocks(){return(0,U.parse)(e.content)}},Dn.set(e,t)),t}function Vn(e){return{...e,title:e?.title?.raw||e?.title||"",content:e?.content?.raw||e?.content||""}}const Hn=(0,l.createRegistrySelector)((e=>(t,o)=>!!e(ue.store).get(B,o))),Gn=(0,l.createRegistrySelector)((e=>()=>!!e(Fe).getActiveComplementaryArea(B))),$n=(0,l.createRegistrySelector)((e=>()=>{const t=e(B).getEmailPostId();return!!e(re.store).hasEditsForEntityRecord("postType",R,t)})),Un=(0,l.createRegistrySelector)((e=>()=>{const t=e(B).getEmailPostId();return!!e(re.store).getEntityRecord("postType",R,t)})),Wn=(0,l.createRegistrySelector)((e=>()=>{const t=e(B).getEmailPostId();return!!e(re.store).isSavingEntityRecord("postType",R,t)})),qn=(0,l.createRegistrySelector)((e=>()=>{const t=e(B).getEmailPostId(),o=e(re.store).getEntityRecord("postType",R,t);if(!o)return!0;const{content:n,title:s}=o;return!n.raw&&!s.raw})),Zn=(0,l.createRegistrySelector)((e=>()=>{const t=e(B).getEmailPostId(),o=e(re.store).getEntityRecord("postType",R,t);if(!o)return!0;const{content:n}=o;return!n.raw})),Yn=(0,l.createRegistrySelector)((e=>()=>{const t=e(B).getEmailPostId(),o=e(re.store).getEntityRecord("postType",R,t);return!!o&&"sent"===o.status})),Jn=(0,l.createRegistrySelector)((e=>()=>{const t=e(B).getEmailPostId(),o=e(re.store).getEditedEntityRecord("postType",R,t);return o?zn(o):""})),Xn=(0,l.createRegistrySelector)((e=>()=>e(re.store).getEntityRecords("postType",R,{per_page:30,status:"publish,sent"})?.filter((e=>""!==e?.content?.raw))||[])),Kn=(0,l.createRegistrySelector)((e=>(0,l.createSelector)((()=>e(re.store).getBlockPatterns().filter((({templateTypes:e})=>Array.isArray(e)&&e.includes("email-template"))).map(On)),(()=>[e(re.store).getBlockPatterns()])))),Qn=(0,l.createRegistrySelector)((e=>()=>e(re.store).canUser("create",{kind:"postType",name:"wp_template"})));function es(e,t){return Qn()?e(re.store).getEditedEntityRecord("postType","wp_template",t):Vn(e(re.store).getEntityRecord("postType","wp_template",t,{context:"view"}))}const ts=(0,l.createRegistrySelector)((e=>()=>{const t=e(se.store).getEditedPostAttribute("template");if(t){const o=e(re.store).getEntityRecords("postType","wp_template",{per_page:-1,context:"view"})?.find((e=>e.slug===t));return o?es(e,o.id):Vn(o)}const o=e(re.store).getDefaultTemplateId({slug:"email-general"});return es(e,o)})),os=(0,l.createRegistrySelector)((e=>()=>{if("wp_template"===e(se.store).getCurrentPostType()){const t=e(se.store).getCurrentPostId();return e(re.store).getEditedEntityRecord("postType","wp_template",t)}return ts()})),ns=()=>{const e=os();return e?zn(e):""},ss=(0,l.createRegistrySelector)((e=>()=>{const t=e(B).getGlobalStylesPostId();return{postId:t,canEdit:e(re.store).canUser("update",{kind:"root",name:"globalStyles",id:t})}})),rs=(0,l.createRegistrySelector)((e=>()=>{const{postId:t,canEdit:o}=ss();return t?o?e(re.store).getEditedEntityRecord("postType","wp_global_styles",t):Vn(e(re.store).getEntityRecord("postType","wp_global_styles",t,{context:"view"})):ts()})),is=(0,l.createRegistrySelector)((e=>()=>e(re.store).getEntityRecords("postType","wp_template",{per_page:-1,post_type:R,context:"view"})?.filter((e=>e.post_types.includes(R)))));function as(e){return e.postId}function ls(e){return e.settingsSidebar.activeTab}function cs(e){return e.editorSettings}function ds(e){return e.editorSettings.__experimentalFeatures.color.palette}function ms(e){return e.preview}function ps(e){return e.personalizationTags}function us(e){return e.personalizationTags.list}const _s=(0,l.createRegistrySelector)((e=>()=>e(se.store).getDeviceType()));function hs(e){return e.theme.styles}function gs(e){return e.autosaveInterval}function xs(e){return e.theme}function ys(e){return e.styles.globalStylesPostId}function ws(e){return e.urls}function*fs(){const e=yield(0,l.select)(B),t=e.personalizationTags?.isFetching;if(!t){yield Fn(!0);try{const e=yield(0,wn.apiFetch)({path:"/woocommerce-email-editor/v1/get_personalization_tags",method:"GET"});yield Rn(e.result)}finally{yield Fn(!1)}}}const vs=window.wp.keyboardShortcuts;function bs(){const{undo:e,redo:t}=(0,l.useDispatch)(re.store),{isSidebarOpened:o,hasEdits:n,isSaving:s}=(0,l.useSelect)((e=>({isSidebarOpened:e(B).isSidebarOpened(),isSaving:e(B).isSaving(),hasEdits:e(B).hasEdits()}))),{openSidebar:r,closeSidebar:i,saveEditedEmail:a,toggleFeature:d}=(0,l.useDispatch)(B),{registerShortcut:m}=(0,l.useDispatch)(vs.store);return(0,c.useEffect)((()=>{m({name:"woocommerce/email-editor/toggle-fullscreen",category:"global",description:(0,g.__)("Toggle fullscreen mode.","woocommerce"),keyCombination:{modifier:"secondary",character:"f"}}),m({name:"woocommerce/email-editor/toggle-sidebar",category:"global",description:(0,g.__)("Show or hide the settings sidebar.","woocommerce"),keyCombination:{modifier:"primaryShift",character:","}}),m({name:"woocommerce/email-editor/save",category:"global",description:(0,g.__)("Save your changes.","woocommerce"),keyCombination:{modifier:"primary",character:"s"}}),m({name:"woocommerce/email-editor/undo",category:"block",description:(0,g.__)("Undo your last changes.","woocommerce"),keyCombination:{modifier:"primary",character:"z"}}),m({name:"woocommerce/email-editor/redo",category:"block",description:(0,g.__)("Redo your last undo.","woocommerce"),keyCombination:{modifier:"primaryShift",character:"z"}})}),[m]),(0,vs.useShortcut)("woocommerce/email-editor/toggle-fullscreen",(()=>{P("keyboard_shortcuts_toggle_fullscreen"),d("fullscreenMode")})),(0,vs.useShortcut)("woocommerce/email-editor/toggle-sidebar",(e=>{P("keyboard_shortcuts_toggle_sidebar"),e.preventDefault(),o?i():r()})),(0,vs.useShortcut)("woocommerce/email-editor/save",(e=>{P("keyboard_shortcuts_save"),e.preventDefault(),n&&!s&&a()})),(0,vs.useShortcut)("woocommerce/email-editor/undo",(t=>{e(),t.preventDefault()})),(0,vs.useShortcut)("woocommerce/email-editor/redo",(e=>{t(),e.preventDefault()})),null}const js=e=>{(0,d.doAction)("woocommerce_email_editor_events",e.detail)};window.addEventListener("unload",(function(){T&&N.removeEventListener(E,js)}));const Ss=(0,d.applyFilters)("woocommerce_email_editor_wrap_editor_component",(function(){const{postId:e,settings:t}=(0,l.useSelect)((e=>({postId:e(B).getEmailPostId(),settings:e(B).getInitialEditorSettings()})),[]);return(0,_.jsxs)(c.StrictMode,{children:[(0,_.jsx)(bs,{}),(0,_.jsx)(yn,{initialEdits:[],postId:e,postType:R,settings:t})]})}));window.addEventListener("DOMContentLoaded",(()=>{!function(){const e=document.getElementById("woocommerce-email-editor");e&&(T&&N.addEventListener(E,js),(()=>{const e=(0,l.createReduxStore)(B,{actions:r,controls:wn.controls,selectors:i,resolvers:a,reducer:Ln,initialState:{inserterSidebar:{isOpened:!1},listviewSidebar:{isOpened:!1},settingsSidebar:{activeTab:A},postId:L,editorSettings:window.WooCommerceEmailEditor.editor_settings,theme:window.WooCommerceEmailEditor.editor_theme,styles:{globalStylesPostId:window.WooCommerceEmailEditor.user_theme_post_id},autosaveInterval:60,urls:window.WooCommerceEmailEditor.urls,preview:{deviceType:"Desktop",toEmail:window.WooCommerceEmailEditor.current_wp_user_email,isModalOpened:!1,isSendingPreviewEmail:!1,sendingPreviewStatus:null},personalizationTags:{list:[],isFetching:!1}}});(0,l.register)(e)})(),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/layout/addAttribute",ee),(0,d.addFilter)("editor.BlockListBlock","woocommerce-email-editor/with-layout-styles",ne),(0,d.addFilter)("editor.BlockEdit","woocommerce-email-editor/with-inspector-controls",te),(0,d.addFilter)("editor.BlockEdit","woocommerce-email-editor/deactivate-stack-on-mobile",h),(0,d.addFilter)("editor.BlockEdit","woocommerce-email-editor/hide-expand-on-click",y),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/deactivate-image-filter",((e,t)=>"core/image"===t?{...e,supports:{...e.supports,filter:{duetone:!1}}}:e)),(0,w.unregisterFormatType)("core/image"),(0,w.unregisterFormatType)("core/code"),(0,w.unregisterFormatType)("core/language"),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-columns-layout",((e,t)=>"core/columns"===t||"core/column"===t?{...e,supports:{...e.supports,layout:!1}}:e)),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-group-variations",((e,t)=>"core/group"===t?{...e,variations:e.variations.filter((e=>"group"===e.name)),supports:{...e.supports,layout:!1}}:e)),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-button",((e,t)=>"core/button"===t?{...e,styles:[]}:e)),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-buttons",((e,t)=>"core/buttons"===t?{...e,supports:{...e.supports,layout:!1,__experimentalEmailFlexLayout:!0}}:e)),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-column",((e,t)=>"core/column"===t?{...e,supports:{...e.supports,background:{backgroundImage:!0}}}:e)),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-columns",((e,t)=>"core/columns"===t?{...e,supports:{...e.supports,background:{backgroundImage:!0}}}:e)),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-post-content",((e,t)=>{return"core/post-content"===t?{...e,edit:(o=e.edit,function({context:e,__unstableLayoutClassNames:t}){const{postId:n,postType:s}=e;return n&&s?(0,_.jsx)(o,{context:e,__unstableLayoutClassNames:t}):(0,_.jsx)(x,{layoutClassNames:t})})}:e;var o})),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-quote",((e,t)=>"core/quote"===t?{...e,styles:[],supports:{...e.supports,align:[]}}:e)),(0,w.registerFormatType)("woocommerce-email-editor/shortcode",{name:"woocommerce-email-editor/shortcode",title:(0,g.__)("Personalization Tags","woocommerce"),className:"woocommerce-email-editor-personalization-tags",tagName:"span",attributes:{},edit:V}),(0,w.registerFormatType)("woocommerce-email-editor/link-shortcode",{name:"woocommerce-email-editor/link-shortcode",title:(0,g.__)("Personalization Tags Link","woocommerce"),className:"woocommerce-email-editor-personalization-tags-link",tagName:"a",attributes:{"data-link-href":"data-link-href",contenteditable:"contenteditable",style:"style"},edit:null}),(0,d.addFilter)("editor.BlockEdit","woocommerce-email-editor/with-live-content-update",H),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/block-support",(e=>e.supports?.shadow?{...e,supports:{...e.supports,shadow:!1}}:e)),(0,m.registerCoreBlocks)(),(0,d.addFilter)("editor.MediaUpload","woocommerce/email-editor/replace-media-upload",(()=>ie.MediaUpload)),(0,c.createRoot)(e).render((0,_.jsx)(Ss,{})))}()}))},894:(e,t)=>{var o;!function(){"use strict";var n={}.hasOwnProperty;function s(){for(var e=[],t=0;t<arguments.length;t++){var o=arguments[t];if(o){var r=typeof o;if("string"===r||"number"===r)e.push(o);else if(Array.isArray(o)){if(o.length){var i=s.apply(null,o);i&&e.push(i)}}else if("object"===r){if(o.toString!==Object.prototype.toString&&!o.toString.toString().includes("[native code]")){e.push(o.toString());continue}for(var a in o)n.call(o,a)&&o[a]&&e.push(a)}}}return e.join(" ")}e.exports?(s.default=s,e.exports=s):void 0===(o=function(){return s}.apply(t,[]))||(e.exports=o)}()},192:e=>{"use strict";var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===o}(e)}(e)},o="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?a((o=e,Array.isArray(o)?[]:{}),e,t):e;var o}function s(e,t,o){return e.concat(t).map((function(e){return n(e,o)}))}function r(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function i(e,t){try{return t in e}catch(e){return!1}}function a(e,o,l){(l=l||{}).arrayMerge=l.arrayMerge||s,l.isMergeableObject=l.isMergeableObject||t,l.cloneUnlessOtherwiseSpecified=n;var c=Array.isArray(o);return c===Array.isArray(e)?c?l.arrayMerge(e,o,l):function(e,t,o){var s={};return o.isMergeableObject(e)&&r(e).forEach((function(t){s[t]=n(e[t],o)})),r(t).forEach((function(r){(function(e,t){return i(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,r)||(i(e,r)&&o.isMergeableObject(t[r])?s[r]=function(e,t){if(!t.customMerge)return a;var o=t.customMerge(e);return"function"==typeof o?o:a}(r,o)(e[r],t[r],o):s[r]=n(t[r],o))})),s}(e,o,l):n(o,l)}a.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,o){return a(e,o,t)}),{})};var l=a;e.exports=l}},o={};function n(e){var s=o[e];if(void 0!==s)return s.exports;var r=o[e]={exports:{}};return t[e](r,r.exports,n),r.exports}n.m=t,e=[],n.O=(t,o,s,r)=>{if(!o){var i=1/0;for(d=0;d<e.length;d++){for(var[o,s,r]=e[d],a=!0,l=0;l<o.length;l++)(!1&r||i>=r)&&Object.keys(n.O).every((e=>n.O[e](o[l])))?o.splice(l--,1):(a=!1,r<i&&(i=r));if(a){e.splice(d--,1);var c=s();void 0!==c&&(t=c)}}return t}r=r||0;for(var d=e.length;d>0&&e[d-1][2]>r;d--)e[d]=e[d-1];e[d]=[o,s,r]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e={57:0,350:0};n.O.j=t=>0===e[t];var t=(t,o)=>{var s,r,[i,a,l]=o,c=0;if(i.some((t=>0!==e[t]))){for(s in a)n.o(a,s)&&(n.m[s]=a[s]);if(l)var d=l(n)}for(t&&t(o);c<i.length;c++)r=i[c],n.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return n.O(d)},o=globalThis.webpackChunk_woocommerce_email_editor=globalThis.webpackChunk_woocommerce_email_editor||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var s=n.O(void 0,[350],(()=>n(492)));s=n.O(s)})();
# Dependencies
/omniservice-plugin/node_modules
/omniservice-plugin/vendor
/omniservice-plugin/spa/

# WordPress Core & Sensitive Files
/wp-admin/
/wp-includes/
/wp-content/plugins/!omniservice-plugin/
/wp-content/themes/
/wp-config.php
/.htaccess

# Build files & logs
/.idea
/.vscode
/npm-debug.log
/yarn-error.log
*.log

# OS-generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
*.sublime-project
*.sublime-workspace
.vscode/
.idea/
*.iml

# Compiled assets
/omniservice-plugin/spa/static/
/omniservice-plugin/spa/index.html

# Test coverage
/coverage/
/.nyc_output/

# Package manager lock files (keep one, remove others based on preference)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Composer
/omniservice-plugin/composer.lock

# WordPress specific
wp-config-local.php
wp-config-staging.php
wp-config-production.php

# Database dumps
*.sql
*.sql.gz

# Cache files
*.cache
/cache/

# Backup files
*.bak
*.backup

# System files
Thumbs.db
Desktop.ini

# JetBrains IDEs
.idea/
*.iws
*.iml
*.ipr

# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

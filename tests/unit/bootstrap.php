<?php
/**
 * PHPUnit bootstrap file for OmniService unit tests
 *
 * @package OmniService
 */

// Define test environment
define('OMNISERVICE_TESTS', true);

// WordPress test environment
$_tests_dir = getenv('WP_TESTS_DIR');
if (!$_tests_dir) {
    $_tests_dir = '/tmp/wordpress-tests-lib';
}

// Give access to tests_add_filter() function
require_once $_tests_dir . '/includes/functions.php';

/**
 * Manually load the plugin being tested
 */
function _manually_load_plugin() {
    require dirname(dirname(__DIR__)) . '/omniservice-plugin/omniservice.php';
}
tests_add_filter('muplugins_loaded', '_manually_load_plugin');

// Start up the WP testing environment
require $_tests_dir . '/includes/bootstrap.php';

// Load Composer autoloader if available
$composer_autoload = dirname(dirname(__DIR__)) . '/omniservice-plugin/vendor/autoload.php';
if (file_exists($composer_autoload)) {
    require_once $composer_autoload;
}

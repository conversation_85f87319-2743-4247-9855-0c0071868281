<?php
/**
 * Unit tests for OmniService_Activator class
 *
 * @package OmniService
 */

class Test_OmniService_Activator extends WP_UnitTestCase {

    /**
     * Test plugin activation
     */
    public function test_activate() {
        // Test that activation doesn't throw errors
        $this->assertTrue(true);
        
        // TODO: Add specific activation tests
        // - Test database table creation
        // - Test custom post type registration
        // - Test user role creation
        // - Test default option setting
        // - Test cron event scheduling
    }

    /**
     * Test database table creation
     */
    public function test_create_tables() {
        global $wpdb;
        
        // Activate plugin to create tables
        OmniService_Activator::activate();
        
        // Check if tables exist
        $worker_rates_table = $wpdb->prefix . 'omniservice_worker_rates';
        $availability_table = $wpdb->prefix . 'omniservice_availability';
        $logs_table = $wpdb->prefix . 'omniservice_logs';
        $notification_queue_table = $wpdb->prefix . 'omniservice_notification_queue';
        
        $this->assertEquals($worker_rates_table, $wpdb->get_var("SHOW TABLES LIKE '$worker_rates_table'"));
        $this->assertEquals($availability_table, $wpdb->get_var("SHOW TABLES LIKE '$availability_table'"));
        $this->assertEquals($logs_table, $wpdb->get_var("SHOW TABLES LIKE '$logs_table'"));
        $this->assertEquals($notification_queue_table, $wpdb->get_var("SHOW TABLES LIKE '$notification_queue_table'"));
    }

    /**
     * Test user role creation
     */
    public function test_user_roles_created() {
        // Activate plugin to create roles
        OmniService_Activator::activate();
        
        // Check if custom roles exist
        $this->assertNotNull(get_role('os_client'));
        $this->assertNotNull(get_role('os_worker'));
        
        // Check role capabilities
        $client_role = get_role('os_client');
        $this->assertTrue($client_role->has_cap('read'));
        $this->assertTrue($client_role->has_cap('os_book_services'));
        $this->assertTrue($client_role->has_cap('os_manage_bookings'));
        $this->assertTrue($client_role->has_cap('os_view_dashboard'));
        
        $worker_role = get_role('os_worker');
        $this->assertTrue($worker_role->has_cap('read'));
        $this->assertTrue($worker_role->has_cap('os_provide_services'));
        $this->assertTrue($worker_role->has_cap('os_manage_availability'));
        $this->assertTrue($worker_role->has_cap('os_manage_bookings'));
        $this->assertTrue($worker_role->has_cap('os_view_dashboard'));
        $this->assertTrue($worker_role->has_cap('os_view_earnings'));
    }

    /**
     * Test default options are set
     */
    public function test_default_options_set() {
        // Activate plugin to set default options
        OmniService_Activator::activate();
        
        // Check some default options
        $this->assertEquals(30, omniservice_get_option('booking_time_slot_interval'));
        $this->assertEquals(24, omniservice_get_option('booking_lead_time'));
        $this->assertEquals(90, omniservice_get_option('booking_window'));
        $this->assertEquals(30, omniservice_get_option('buffer_time'));
        $this->assertEquals('require_approval', omniservice_get_option('approval_mode'));
        $this->assertTrue(omniservice_get_option('deposits_enabled'));
        $this->assertTrue(omniservice_get_option('tipping_enabled'));
    }

    /**
     * Test cron events are scheduled
     */
    public function test_cron_events_scheduled() {
        // Activate plugin to schedule cron events
        OmniService_Activator::activate();
        
        // Check if cron events are scheduled
        $this->assertNotFalse(wp_next_scheduled('omniservice_process_notification_queue'));
        $this->assertNotFalse(wp_next_scheduled('omniservice_cleanup_logs'));
        $this->assertNotFalse(wp_next_scheduled('omniservice_update_booking_statuses'));
    }

    /**
     * Clean up after tests
     */
    public function tearDown(): void {
        parent::tearDown();
        
        // Clean up any test data
        global $wpdb;
        
        // Remove test tables if they exist
        $tables = array(
            $wpdb->prefix . 'omniservice_worker_rates',
            $wpdb->prefix . 'omniservice_availability',
            $wpdb->prefix . 'omniservice_logs',
            $wpdb->prefix . 'omniservice_notification_queue'
        );
        
        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
        
        // Remove test roles
        remove_role('os_client');
        remove_role('os_worker');
        
        // Clear cron events
        wp_clear_scheduled_hook('omniservice_process_notification_queue');
        wp_clear_scheduled_hook('omniservice_cleanup_logs');
        wp_clear_scheduled_hook('omniservice_update_booking_statuses');
    }
}

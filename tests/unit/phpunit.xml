<?xml version="1.0" encoding="UTF-8"?>
<phpunit
    bootstrap="bootstrap.php"
    backupGlobals="false"
    colors="true"
    convertErrorsToExceptions="true"
    convertNoticesToExceptions="true"
    convertWarningsToExceptions="true"
    processIsolation="false"
    stopOnFailure="false"
    verbose="true"
>
    <testsuites>
        <testsuite name="OmniService Unit Tests">
            <directory>./</directory>
        </testsuite>
    </testsuites>
    
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">../../omniservice-plugin/includes</directory>
            <directory suffix=".php">../../omniservice-plugin/admin</directory>
            <exclude>
                <directory>../../omniservice-plugin/vendor</directory>
                <directory>../../omniservice-plugin/node_modules</directory>
            </exclude>
        </whitelist>
    </filter>
    
    <logging>
        <log type="coverage-html" target="coverage-html"/>
        <log type="coverage-clover" target="coverage.xml"/>
    </logging>
</phpunit>

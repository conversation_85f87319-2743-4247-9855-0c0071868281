# OmniService Testing

This directory contains all tests for the OmniService plugin.

## Directory Structure

```
tests/
├── unit/           # PHPUnit unit tests
├── integration/    # Integration tests
├── e2e/           # End-to-end tests
└── README.md      # This file
```

## Unit Tests

Unit tests are located in the `unit/` directory and use PHPUnit for testing individual PHP classes and functions.

### Running Unit Tests

```bash
cd tests/unit
phpunit
```

### Test Configuration

- Configuration: `phpunit.xml`
- Bootstrap: `bootstrap.php`
- Test files: `test-*.php`

### Writing Unit Tests

1. Create test files with the naming convention `test-[class-name].php`
2. Extend `WP_UnitTestCase` for WordPress-specific tests
3. Use descriptive test method names starting with `test_`
4. Include setup and teardown methods as needed

Example:
```php
class Test_OmniService_Example extends WP_UnitTestCase {
    
    public function setUp(): void {
        parent::setUp();
        // Setup code here
    }
    
    public function test_example_functionality() {
        // Test code here
        $this->assertTrue(true);
    }
    
    public function tearDown(): void {
        // Cleanup code here
        parent::tearDown();
    }
}
```

## Integration Tests

Integration tests verify that different components work together correctly.

### Running Integration Tests

```bash
cd tests/integration
phpunit
```

### Test Scenarios

- WordPress integration
- WooCommerce integration
- Database operations
- API endpoints
- User role interactions

## End-to-End Tests

E2E tests simulate real user interactions using browser automation.

### Tools

- **Cypress** (recommended) or **Playwright**
- Tests written in JavaScript/TypeScript

### Running E2E Tests

```bash
cd tests/e2e
npm install
npm test
```

### Test Scenarios

- User registration and login
- Booking flow
- Worker dashboard functionality
- Admin panel operations
- Payment processing

## Test Data

### Fixtures

Create test data fixtures for consistent testing:

- Sample users (clients, workers, admins)
- Sample services
- Sample bookings
- Sample settings

### Database

Tests should use a separate test database to avoid affecting production data.

## Continuous Integration

Tests should be run automatically on:

- Pull requests
- Commits to main branch
- Scheduled intervals

### GitHub Actions Example

```yaml
name: Tests
on: [push, pull_request]
jobs:
  phpunit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '7.4'
      - name: Install dependencies
        run: composer install
      - name: Run tests
        run: vendor/bin/phpunit
```

## Coverage

Aim for high test coverage:

- Unit tests: 80%+ coverage
- Integration tests: Cover critical paths
- E2E tests: Cover main user journeys

### Generating Coverage Reports

```bash
phpunit --coverage-html coverage/
```

## Best Practices

1. **Test Isolation**: Each test should be independent
2. **Descriptive Names**: Test names should clearly describe what is being tested
3. **Arrange-Act-Assert**: Structure tests with clear setup, execution, and verification
4. **Mock External Dependencies**: Use mocks for external services
5. **Test Edge Cases**: Include tests for error conditions and edge cases
6. **Keep Tests Fast**: Unit tests should run quickly
7. **Regular Maintenance**: Update tests when code changes

## Debugging Tests

### PHPUnit Debugging

```bash
# Run specific test
phpunit tests/unit/test-specific-class.php

# Run with verbose output
phpunit --verbose

# Run with debug output
phpunit --debug
```

### WordPress Test Environment

Set up WordPress test environment:

```bash
# Install WordPress test suite
bash bin/install-wp-tests.sh wordpress_test root '' localhost latest
```

## Test Categories

### Critical Tests (Must Pass)

- User authentication
- Booking creation and management
- Payment processing
- Data security

### Important Tests (Should Pass)

- Email notifications
- Calendar integration
- Reporting features
- Admin functionality

### Nice-to-Have Tests

- UI/UX improvements
- Performance optimizations
- Additional features

## Reporting Issues

When tests fail:

1. Check the test output for specific error messages
2. Verify test environment setup
3. Check for recent code changes that might affect the test
4. Create detailed bug reports with reproduction steps

## Contributing

When adding new features:

1. Write tests first (TDD approach)
2. Ensure all existing tests pass
3. Add appropriate test coverage for new code
4. Update this documentation if needed

## Resources

- [PHPUnit Documentation](https://phpunit.de/documentation.html)
- [WordPress Unit Testing](https://make.wordpress.org/core/handbook/testing/automated-testing/phpunit/)
- [WooCommerce Testing](https://github.com/woocommerce/woocommerce/wiki/Unit-tests)
- [Cypress Documentation](https://docs.cypress.io/)
- [Playwright Documentation](https://playwright.dev/)

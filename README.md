# OmniService WordPress Plugin

**Version:** 1.0.0
**Requires WordPress:** 5.8+
**Tested up to:** 6.4
**Requires PHP:** 7.4+
**License:** GPL v2 or later

OmniService is a comprehensive service marketplace and booking platform for WordPress. It enables businesses to manage service bookings, worker schedules, payments, and customer relationships through a powerful, integrated system.

## 🚀 Key Features

- **Complete Booking System** - Real-time availability, recurring bookings, and conflict prevention
- **Multi-Role Management** - Clients, Workers, and Admins with distinct capabilities and dashboards
- **Payment Integration** - Full WooCommerce integration with deposits, final payments, and tips
- **Google Calendar Sync** - Bidirectional synchronization for worker availability
- **Advanced Notifications** - Email and SMS notifications with customizable templates
- **Security & Monitoring** - Comprehensive logging, audit trails, and security features
- **REST API** - Complete API for mobile apps and third-party integrations
- **Translation Ready** - Fully translatable with included .pot file

## 📋 Requirements

### Minimum Requirements
- **WordPress:** 5.8 or higher
- **PHP:** 7.4 or higher (8.0+ recommended)
- **MySQL:** 5.6 or higher (8.0+ recommended)
- **WooCommerce:** 6.0 or higher (required for payments)

### Recommended Server Configuration
- **Memory Limit:** 256MB or higher
- **Max Execution Time:** 60 seconds or higher
- **File Upload Size:** 32MB or higher
- **SSL Certificate:** Required for secure payments and API access

## 📦 Installation

### Automatic Installation (Recommended)

1. **Download the Plugin**
   - Download the latest release from the [releases page](https://github.com/your-repo/omniservice/releases)
   - Or clone this repository: `git clone https://github.com/your-repo/omniservice.git`

2. **Install via WordPress Admin**
   - Go to **Plugins > Add New > Upload Plugin**
   - Choose the `omniservice-plugin.zip` file
   - Click **Install Now** and then **Activate**

3. **Install WooCommerce** (if not already installed)
   - Go to **Plugins > Add New**
   - Search for "WooCommerce" and install the official plugin
   - Complete the WooCommerce setup wizard

### Manual Installation

1. **Upload Plugin Files**
   ```bash
   # Upload the omniservice-plugin folder to your WordPress plugins directory
   /wp-content/plugins/omniservice-plugin/
   ```

2. **Set File Permissions**
   ```bash
   # Ensure proper file permissions
   chmod -R 755 /wp-content/plugins/omniservice-plugin/
   ```

3. **Activate the Plugin**
   - Go to **Plugins** in your WordPress admin
   - Find "OmniService" and click **Activate**

## ⚙️ Initial Setup

### 1. Basic Configuration

After activation, you'll see a welcome screen. Follow these steps:

1. **Go to OmniService Settings**
   - Navigate to **OmniService > Settings** in your WordPress admin
   - Configure basic settings like business information and time zones

2. **Configure WooCommerce Integration**
   - Go to **Settings > Payments** tab
   - Enable WooCommerce integration
   - Configure deposit settings (recommended: 25-50% of service cost)

3. **Set Up User Roles**
   - The plugin automatically creates worker and client roles
   - Adjust capabilities if needed in **Settings > Roles & Permissions**

### 2. Create Your First Service

1. **Add a New Service**
   - Go to **OmniService > Services > Add New**
   - Fill in service details (name, description, duration, price)
   - Set service category and featured image

2. **Configure Service Settings**
   - Set booking advance notice (e.g., 24 hours)
   - Configure cancellation policy
   - Set maximum bookings per day/week

### 3. Add Workers

1. **Create Worker Accounts**
   - Go to **OmniService > Workers > Add New**
   - Create user accounts with the "Worker" role
   - Or allow workers to register via the frontend form

2. **Assign Workers to Services**
   - Edit each service and assign qualified workers
   - Set individual hourly rates for each worker
   - Configure worker-specific availability

### 4. Configure Notifications

1. **Email Settings**
   - Go to **Settings > Notifications**
   - Configure SMTP settings for reliable email delivery
   - Customize email templates for different events

2. **SMS Settings (Optional)**
   - Configure SMS gateway integration
   - Set up SMS templates for critical notifications
   - Test SMS delivery

## 🔧 Advanced Configuration

### Google Calendar Integration

1. **Create Google API Credentials**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable the Google Calendar API
   - Create OAuth 2.0 credentials

2. **Configure in OmniService**
   - Go to **Settings > Integrations > Google Calendar**
   - Enter your Client ID and Client Secret
   - Set the redirect URI as shown in the settings

3. **Connect Worker Calendars**
   - Workers can connect their Google Calendars from their dashboard
   - Automatic bidirectional sync will begin immediately

### Payment Gateway Setup

1. **WooCommerce Payment Methods**
   - Configure your preferred payment gateways in WooCommerce
   - Recommended: Stripe, PayPal, or Square for best integration

2. **Deposit Configuration**
   - Set default deposit percentage (25-50% recommended)
   - Configure deposit payment flow
   - Set up automatic final payment processing

### Security Configuration

1. **Enable Security Features**
   - Go to **Settings > Security**
   - Enable failed login monitoring
   - Configure IP blocking rules
   - Set up security notifications

2. **API Security**
   - Generate API keys for third-party integrations
   - Configure rate limiting
   - Set up CORS policies if needed

## 🎨 Customization

### Frontend Customization

1. **Booking Form Styling**
   - Use the built-in style customizer
   - Or add custom CSS in **Appearance > Customize > Additional CSS**

2. **Page Templates**
   - Copy template files to your theme directory
   - Customize booking flow pages
   - Modify email templates

### Shortcodes

Use these shortcodes to display OmniService content:

```php
[omniservice_booking_form]          // Display booking form
[omniservice_services]              // List all services
[omniservice_worker_dashboard]      // Worker dashboard (logged-in workers only)
[omniservice_client_dashboard]      // Client dashboard (logged-in clients only)
[omniservice_calendar]              // Display availability calendar
```

## 🔍 Troubleshooting

### Common Issues

**Plugin Activation Errors**
- Ensure WordPress and PHP meet minimum requirements
- Check for plugin conflicts by deactivating other plugins
- Verify file permissions are set correctly

**Booking Form Not Working**
- Ensure WooCommerce is installed and activated
- Check that required pages are published
- Verify shortcodes are placed correctly

**Email Notifications Not Sending**
- Configure SMTP settings in **Settings > Notifications**
- Test email delivery using the built-in test tool
- Check spam folders and email server logs

**Google Calendar Sync Issues**
- Verify API credentials are correct
- Check that OAuth redirect URI matches exactly
- Ensure workers have connected their calendars

### Debug Mode

Enable debug mode for troubleshooting:

```php
// Add to wp-config.php
define('OMNISERVICE_DEBUG', true);
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

Check debug logs in `/wp-content/debug.log` and `/wp-content/uploads/omniservice-logs/`

### Getting Help

1. **Documentation**: Check the [Technical Specification](TECHNICAL_SPECIFICATION.md)
2. **Testing Guide**: Follow the [Testing Plan](omniservice-plugin/TESTING_PLAN.md)
3. **Support**: Contact <EMAIL>
4. **Community**: Join our [Discord community](https://discord.gg/omniservice)

## 🏗️ Architecture Overview

### Plugin Structure
```
omniservice-plugin/
├── 📂 admin/                    # Admin interface and dashboard
├── 📂 includes/                 # Core plugin functionality
│   ├── 📂 api/                 # REST API endpoints
│   ├── 📂 booking-engine/      # Booking management system
│   ├── 📂 core/                # Core classes and utilities
│   ├── 📂 integrations/        # Third-party integrations
│   └── 📂 notifications/       # Notification system
├── 📂 public/                   # Frontend functionality
├── 📂 languages/                # Translation files
└── 📂 assets/                   # CSS, JS, and image files
```

### Technology Stack
- **Backend:** Object-oriented PHP following WordPress standards
- **Database:** WordPress Custom Post Types + optimized custom tables
- **Frontend:** Vanilla JavaScript with modern ES6+ features
- **Payments:** Deep WooCommerce integration
- **API:** Comprehensive REST API with authentication
- **Integrations:** Google Calendar, SMS gateways, email services

## 🔌 API Documentation

### Authentication
```php
// Get API token
POST /wp-json/omniservice/v1/auth/login
{
    "username": "your_username",
    "password": "your_password"
}
```

### Key Endpoints
```php
// Get services
GET /wp-json/omniservice/v1/services

// Create booking
POST /wp-json/omniservice/v1/bookings
{
    "service_id": 123,
    "worker_id": 456,
    "start_time": "2024-01-15 10:00:00",
    "end_time": "2024-01-15 12:00:00"
}

// Get availability
GET /wp-json/omniservice/v1/availability?worker_id=456&date=2024-01-15
```

## 🧪 Development & Testing

### Local Development Setup

1. **Clone Repository**
   ```bash
   git clone https://github.com/your-repo/omniservice.git
   cd omniservice
   ```

2. **Install Dependencies**
   ```bash
   cd omniservice-plugin
   composer install --dev
   ```

3. **Run Tests**
   ```bash
   # Run all tests
   composer test

   # Run specific test suite
   vendor/bin/phpunit tests/unit/
   ```

### Code Quality Standards

- **PHP:** PSR-12 coding standards
- **WordPress:** WordPress Coding Standards (WPCS)
- **Security:** All inputs sanitized, outputs escaped
- **Database:** Prepared statements only
- **Documentation:** PHPDoc for all public methods

## 🤝 Contributing

We welcome contributions! Please follow these guidelines:

1. **Fork the Repository**
2. **Create Feature Branch** (`git checkout -b feature/amazing-feature`)
3. **Follow Coding Standards** (PSR-12 for PHP, WordPress standards)
4. **Write Tests** for new functionality
5. **Update Documentation** as needed
6. **Submit Pull Request**

### Development Workflow
- **Main Branch:** Production-ready code
- **Develop Branch:** Integration branch for features
- **Feature Branches:** Individual feature development
- **Hotfix Branches:** Critical bug fixes

## 📞 Support & Community

### Getting Help
- **Documentation:** [Technical Specification](TECHNICAL_SPECIFICATION.md)
- **Testing Guide:** [Testing Plan](omniservice-plugin/TESTING_PLAN.md)
- **Email Support:** <EMAIL>
- **Community Forum:** [Visit our forum](https://community.omniservice.com)

### Reporting Issues
1. Check existing issues on GitHub
2. Provide detailed reproduction steps
3. Include WordPress/PHP version information
4. Attach relevant log files

### Feature Requests
- Submit feature requests via GitHub Issues
- Use the "Feature Request" template
- Provide detailed use cases and benefits

## 📄 License

This plugin is licensed under the GPL v2 or later.

```
OmniService WordPress Plugin
Copyright (C) 2024 OmniService Team

This program is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 2 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.
```

## 🎉 Changelog

### Version 1.0.0 (2024-01-XX)
- Initial release
- Complete booking system with real-time availability
- WooCommerce payment integration with deposits
- Google Calendar bidirectional synchronization
- Multi-role user management system
- Comprehensive notification system
- REST API for third-party integrations
- Security and health monitoring features
- Translation-ready with included .pot file

---

**Made with ❤️ by the OmniService Team**
